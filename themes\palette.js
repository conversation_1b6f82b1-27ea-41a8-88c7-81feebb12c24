// material-ui
import { createTheme } from "@mui/material/styles";

// project import
import ThemeOption from "./theme";

// ==============================|| SWISS-AFGHAN RESTAURANT THEME - PALETTE  ||============================== //

const Palette = (mode) => {
  // Custom Swiss-Afghan Restaurant Color Palette
  const colors = {
    // Swiss-inspired reds (from flag and alpine sunsets)
    red: [
      "#FFF5F5",   // 0 - Very light red
      "#FFE0E0",   // 1 - Light red
      "#FFB3B3",   // 2 - Soft red
      "#FF8080",   // 3 - Medium red
      "#FF4D4D",   // 4 - Bright red
      "#DC143C",   // 5 - Swiss flag red
      "#B91C3C",   // 6 - Darker red
      "#991B1B",   // 7 - Deep red
      "#7F1D1D",   // 8 - Very deep red
      "#651E1E"    // 9 - Darkest red
    ],

    // Afghan-inspired golds (traditional jewelry and spices)
    gold: [
      "#FDF8F0",   // 0 - Cream white
      "#F9ECCE",   // 1 - Warm cream
      "#F2D89D",   // 2 - Light gold
      "#EBC46C",   // 3 - Afghan gold
      "#E4B03B",   // 4 - Rich gold
      "#D4941A",   // 5 - Deep Afghan gold
      "#B87F16",   // 6 - Darker gold
      "#9C6A12",   // 7 - Bronze gold
      "#80550E",   // 8 - Deep bronze
      "#64400A"    // 9 - Darkest gold
    ],

    // Swiss-Afghan cyan (mountain lakes and lapis lazuli)
    cyan: [
      "#F0F9FF",   // 0 - Very light blue
      "#E0F2FE",   // 1 - Light blue
      "#BAE6FD",   // 2 - Soft blue
      "#7DD3FC",   // 3 - Medium blue
      "#38BDF8",   // 4 - Bright blue
      "#0EA5E9",   // 5 - Deep blue
      "#0284C7",   // 6 - Darker blue
      "#0369A1",   // 7 - Rich blue
      "#075985",   // 8 - Deep blue
      "#0C4A6E"    // 9 - Darkest blue
    ],

    // Swiss-Afghan greens (alpine forests and Afghan gardens)
    green: [
      "#F0F8F4",   // 0 - Very light alpine green
      "#D4EDDD",   // 1 - Light mountain mist
      "#A8D4B8",   // 2 - Soft alpine meadow
      "#7BB893",   // 3 - Fresh mountain green
      "#4F9C6E",   // 4 - Rich forest green
      "#2D5D3F",   // 5 - Deep Swiss forest
      "#254F35",   // 6 - Darker forest depth
      "#1D402B",   // 7 - Mountain shadow
      "#153121",   // 8 - Deep evergreen
      "#0D2217"    // 9 - Darkest forest
    ],

    // Restaurant-appropriate greys
    grey: [
      "#FEFEFE",   // 0 - Pure white
      "#FAFAF9",   // 1 - Warm off-white
      "#F5F5F4",   // 2 - Light warm grey
      "#F0F0EF",   // 3 - Soft grey
      "#D9D9D8",   // 4 - Medium light grey
      "#BFBFBE",   // 5 - Medium grey
      "#8C8C8B",   // 6 - Dark medium grey
      "#595958",   // 7 - Dark grey
      "#2D2D2D",   // 8 - Very dark grey
      "#1A1A1A",   // 9 - Almost black
      "#000000",   // 10 - Pure black
      "#FAFAFA",   // 11 - Light accent
      "#BFBFBF",   // 12 - Medium accent
      "#434343",   // 13 - Dark accent
      "#1F1F1F",   // 14 - Very dark accent
      "#FAFAFB",   // 15 - Constant light
      "#E6EBF1"    // 16 - Constant medium
    ]
  };

  const paletteColor = ThemeOption(colors);

  return createTheme({
    palette: {
      mode,
      common: {
        black: "#000000",
        white: "#FFFFFF"  // Pure white for minimalist design
      },
      ...paletteColor,
      text: {
        primary: "#000000",        // Black for main text
        secondary: "#595958",      // Medium grey for secondary text
        disabled: paletteColor.grey[400]
      },
      action: {
        disabled: paletteColor.grey[300],
        hover: "rgba(45, 93, 63, 0.04)",     // Subtle primary color hover
        selected: "rgba(212, 148, 26, 0.08)" // Subtle secondary color selection
      },
      divider: "rgba(45, 93, 63, 0.1)",     // Subtle green divider
      background: {
        paper: "#FFFFFF",          // Pure white for cards/papers
        default: "#FFFFFC"         // Light green theme color for main background
      }
    }
  });
};

export default Palette;
