"use client";
import React from "react";
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
} from "@mui/material";

const AboutSection: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  return (
    <Box
      component="section"
      sx={{
        py: { xs: 8, md: 12 },
        position: "relative",
        overflow: "hidden",
        background: `linear-gradient(135deg, 
          ${theme.palette.background.default} 0%, 
          ${theme.palette.primary[50]} 100%)`,
      }}
    >
      {/* Decorative Background Elements */}
      <Box
        sx={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: 0.03,
          backgroundImage: `
            radial-gradient(circle at 20% 20%, ${theme.palette.afghanBlue.main} 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, ${theme.palette.swissRed.main} 0%, transparent 50%),
            linear-gradient(45deg, transparent 40%, ${theme.palette.primary.main} 50%, transparent 60%)
          `,
          zIndex: 0,
        }}
      />

      {/* Subtle Eastern Motif Pattern */}
      <Box
        sx={{
          position: "absolute",
          top: "10%",
          right: "5%",
          width: 200,
          height: 200,
          opacity: 0.05,
          background: `conic-gradient(from 0deg, 
            ${theme.palette.afghanBlue.main}, 
            ${theme.palette.swissRed.main}, 
            ${theme.palette.primary.main}, 
            ${theme.palette.afghanBlue.main})`,
          borderRadius: "50%",
          filter: "blur(1px)",
          zIndex: 0,
        }}
      />

      <Container maxWidth="lg" sx={{ position: "relative", zIndex: 1 }}>
        <Grid container spacing={4} alignItems="center">
          {/* Content Side */}
          <Grid item xs={12} md={6}>
            <Box sx={{ pr: { md: 4 } }}>
              {/* Section Badge */}
              <Box
                sx={{
                  display: "inline-flex",
                  alignItems: "center",
                  px: 3,
                  py: 1,
                  mb: 3,
                  borderRadius: "50px",
                  background: `linear-gradient(135deg, 
                    ${theme.palette.primary[100]} 0%, 
                    ${theme.palette.primary[50]} 100%)`,
                  border: `1px solid ${theme.palette.primary[200]}`,
                  boxShadow: theme.customShadows.alpine,
                }}
              >
                <Typography
                  variant="caption"
                  sx={{
                    fontWeight: 600,
                    color: theme.palette.primary.dark,
                    textTransform: "uppercase",
                    letterSpacing: "0.1em",
                  }}
                >
                  Our Story
                </Typography>
              </Box>

              {/* Main Heading */}
              <Typography
                variant="h2"
                sx={{
                  mb: 3,
                  fontFamily: "'Playfair Display', serif",
                  fontWeight: 700,
                  color: theme.palette.text.primary,
                  lineHeight: 1.2,
                  background: `linear-gradient(135deg, 
                    ${theme.palette.text.primary} 0%, 
                    ${theme.palette.afghanBlue.main} 100%)`,
                  backgroundClip: "text",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                }}
              >
                The Story of Polo
              </Typography>

              {/* Story Content */}
              <Typography
                variant="body1"
                sx={{
                  mb: 4,
                  color: theme.palette.text.secondary,
                  lineHeight: 1.8,
                  fontSize: "1.1rem",
                }}
              >
                Born from the rich culinary traditions of Afghanistan and refined with Swiss precision, 
                Polo represents more than just a restaurant—it's a bridge between cultures. The name 
                "Polo" comes from the beloved Afghan rice dish, a symbol of hospitality and gathering 
                that brings families together around the table.
              </Typography>

              <Typography
                variant="body1"
                sx={{
                  mb: 4,
                  color: theme.palette.text.secondary,
                  lineHeight: 1.8,
                  fontSize: "1.1rem",
                }}
              >
                Our journey began with a simple vision: to honor the aromatic spices and time-honored 
                recipes of Afghan cuisine while embracing the meticulous attention to detail that 
                defines Swiss culinary excellence.
              </Typography>

              {/* Cultural Elements */}
              <Box
                sx={{
                  display: "flex",
                  gap: 3,
                  flexWrap: "wrap",
                  mt: 4,
                }}
              >
                <Box sx={{ textAlign: "center" }}>
                  <Typography
                    variant="h4"
                    sx={{
                      fontFamily: "'Playfair Display', serif",
                      color: theme.palette.afghanBlue.main,
                      mb: 1,
                    }}
                  >
                    🇦🇫
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{
                      color: theme.palette.text.secondary,
                      fontWeight: 500,
                      textTransform: "uppercase",
                      letterSpacing: "0.1em",
                    }}
                  >
                    Afghan Heritage
                  </Typography>
                </Box>
                <Box sx={{ textAlign: "center" }}>
                  <Typography
                    variant="h4"
                    sx={{
                      fontFamily: "'Playfair Display', serif",
                      color: theme.palette.swissRed.main,
                      mb: 1,
                    }}
                  >
                    🇨🇭
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{
                      color: theme.palette.text.secondary,
                      fontWeight: 500,
                      textTransform: "uppercase",
                      letterSpacing: "0.1em",
                    }}
                  >
                    Swiss Precision
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Grid>

          {/* Image Side */}
          <Grid item xs={12} md={6}>
            <Box
              sx={{
                position: "relative",
                height: { xs: 400, md: 500 },
                borderRadius: 4,
                overflow: "hidden",
                boxShadow: theme.customShadows.warm,
              }}
            >
              {/* Main Image Card */}
              <Card
                sx={{
                  height: "100%",
                  position: "relative",
                  background: `linear-gradient(135deg, 
                    ${theme.palette.primary[100]} 0%, 
                    ${theme.palette.primary[200]} 100%)`,
                  border: `1px solid ${theme.palette.primary[200]}`,
                }}
              >
                <CardContent
                  sx={{
                    height: "100%",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                    textAlign: "center",
                    p: 4,
                  }}
                >
                  {/* Kitchen & Rice Dishes Placeholder */}
                  <Box
                    sx={{
                      fontSize: "4rem",
                      mb: 3,
                      filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.1))",
                    }}
                  >
                    🍚
                  </Box>
                  <Typography
                    variant="h5"
                    sx={{
                      fontFamily: "'Playfair Display', serif",
                      fontWeight: 600,
                      color: theme.palette.text.primary,
                      mb: 2,
                    }}
                  >
                    Authentic Afghan Rice Dishes
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      color: theme.palette.text.secondary,
                      maxWidth: 300,
                      lineHeight: 1.6,
                    }}
                  >
                    From our kitchen to your table, every grain of rice tells a story 
                    of tradition, flavor, and the warmth of Afghan hospitality.
                  </Typography>
                </CardContent>

                {/* Floating Badge */}
                <Box
                  sx={{
                    position: "absolute",
                    top: 20,
                    right: 20,
                    px: 2,
                    py: 1,
                    borderRadius: "20px",
                    background: `rgba(255, 255, 255, 0.9)`,
                    backdropFilter: "blur(10px)",
                    border: `1px solid rgba(255, 255, 255, 0.2)`,
                    boxShadow: theme.customShadows.glass,
                  }}
                >
                  <Typography
                    variant="caption"
                    sx={{
                      fontWeight: 600,
                      color: theme.palette.text.primary,
                      textTransform: "uppercase",
                      letterSpacing: "0.05em",
                    }}
                  >
                    Traditional Kitchen
                  </Typography>
                </Box>
              </Card>

              {/* Decorative Elements */}
              <Box
                sx={{
                  position: "absolute",
                  bottom: -20,
                  left: -20,
                  width: 80,
                  height: 80,
                  borderRadius: "50%",
                  background: `linear-gradient(135deg, 
                    ${theme.palette.afghanBlue.light} 0%, 
                    ${theme.palette.afghanBlue.main} 100%)`,
                  opacity: 0.8,
                  filter: "blur(20px)",
                  zIndex: -1,
                }}
              />
              <Box
                sx={{
                  position: "absolute",
                  top: -15,
                  right: -15,
                  width: 60,
                  height: 60,
                  borderRadius: "50%",
                  background: `linear-gradient(135deg, 
                    ${theme.palette.swissRed.light} 0%, 
                    ${theme.palette.swissRed.main} 100%)`,
                  opacity: 0.6,
                  filter: "blur(15px)",
                  zIndex: -1,
                }}
              />
            </Box>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default AboutSection;
