"use client";
import React, { useState } from "react";
import {
  AppBar,
  Box,
  Container,
  IconButton,
  Typography,
  List,
  ListItem,
  ListItemText,
  Button,
  useMediaQuery,
  useTheme,
  Toolbar,
} from "@mui/material";
import { Menu } from "lucide-react";

const navItems = ["Home", "About", "Contact"];

export default function Header() {
  const [menuOpen, setMenuOpen] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const toggleMenu = () => setMenuOpen((prev) => !prev);

  return (
    <>
      <AppBar
        position="fixed"
        elevation={0}
        sx={{
          zIndex: 1300,
          background: "transparent",
          borderRadius: "0 0 32px 32px",
          boxShadow:
            "0 4px 20px rgba(45, 93, 63, 0.12), 0 1px 4px rgba(0, 0, 0, 0.05)",
        }}
      >
        {/* Glass Layer */}
        <Box
          sx={{
            position: "absolute",
            inset: 0,
            backdropFilter: "blur(10px)",
            backgroundColor: "rgba(255, 255, 255, 0.04)",
            zIndex: 1,
          }}
        />

        <Container
          sx={{
            position: "relative",
            zIndex: 2,
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            minHeight: "64px",
          }}
        >
          {/* Burger + Logo */}
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            {isMobile && (
              <IconButton
                onClick={toggleMenu}
                sx={{
                  height: 50,
                  width: 50,
                  transform: menuOpen ? "rotate(90deg)" : "rotate(0deg)",
                  transition: "all 0.3s ease",
                }}
              >
                <Box
                  sx={{
                    width: 18,
                    height: 8,
                    position: "relative",
                    display: "block",
                    top: "50%",
                  }}
                >
                  <Box
                    sx={{
                      height: 2,
                      backgroundColor: "black",
                      transition: "all 0.4s ease",
                      transform: menuOpen
                        ? "translateY(4px) rotate(45deg)"
                        : "translateY(0px) rotate(0deg)",
                    }}
                  />
                  <Box
                    sx={{
                      mt: "6px",
                      height: 2,
                      backgroundColor: "#fff",
                      transition: "all 0.4s ease",
                      transform: menuOpen
                        ? "translateY(-6px) rotate(-45deg)"
                        : "translateY(0px) rotate(0deg)",
                    }}
                  />
                </Box>
              </IconButton>
            )}
            <Typography
              variant="h6"
              sx={{
                color: "#fff",
                fontWeight: 600,
                fontSize: "1.5rem",
              }}
            >
              Polo
            </Typography>
          </Box>

          {/* Desktop Nav Items */}
          {/* {!isMobile && (
            <Box sx={{ display: "flex", gap: 2 }}>
              {navItems.map((item) => (
                <Button
                  key={item}
                  variant="contained"
                  sx={{
                    backgroundColor: "#000",
                    color: "#fff",
                    borderRadius: "25px",
                    textTransform: "capitalize",
                    px: 3,
                    py: 1,
                    fontWeight: 500,
                    letterSpacing: "0.5px",
                    transition: "all 0.3s ease",
                    "&:hover": {
                      backgroundColor: "#1A1A1A",
                      transform: "translateY(-1px)",
                      boxShadow: "0 6px 20px rgba(0, 0, 0, 0.25)",
                    },
                    "&:active": {
                      transform: "translateY(0)",
                    },
                  }}
                >
                  {item}
                </Button>
              ))}
            </Box>
          )} */}
          {/* Glass Layers */}
          {/* <Box
            sx={{
              position: "absolute",
              inset: 0,
              backdropFilter: "blur(8px)",
              filter: "saturate(120%) brightness(1.15)",
              zIndex: 0,
            }}
          />
          <Box
            sx={{
              position: "absolute",
              inset: 0,
              backgroundColor: "rgba(255, 255, 255, 0.04)",
              zIndex: 1,
            }}
          />
          <Box
            sx={{
              position: "absolute",
              inset: 0,
              zIndex: 2,
              boxShadow: `
              inset 1px 1px 0 rgba(255, 255, 255, 0.1),
              inset 0 0 5px rgba(255, 255, 255, 0.08)`,
              borderRadius: "inherit",
            }}
          /> */}

          {/* Content */}
          {!isMobile && (
            <Container
              sx={{
                position: "relative",
                zIndex: 3,
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                minHeight: "64px",
              }}
            >
              <Toolbar disableGutters sx={{ width: "100%" }}>
                <IconButton
                  color="inherit"
                  aria-label="open drawer"
                  edge="start"
                  // onClick={handleDrawerToggle}
                  sx={{ mr: 2, display: { sm: "none" } }}
                >
                  <Menu />
                </IconButton>
                <Typography
                  variant="h6"
                  component="div"
                  sx={{ flexGrow: 1, display: { xs: "none", sm: "block" } }}
                >
                  Polo
                </Typography>
                <Box sx={{ display: { xs: "none", sm: "block" } }}>
                  {navItems.map((item) => (
                    <Button key={item} variant="text">
                      {item}
                    </Button>
                  ))}
                </Box>
              </Toolbar>
            </Container>
          )}
        </Container>
      </AppBar>

      {/* Overlay Menu (Mobile Only) */}
      {isMobile && (
        <Box
          sx={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100%",
            height: menuOpen ? "100%" : "0",
            overflow: "hidden",
            backgroundColor: "rgba(0, 0, 0, 0.95)",
            transition: "height 0.4s ease",
            zIndex: 1200,
          }}
        >
          <List
            disablePadding
            sx={{
              px: "48px",
              pt: menuOpen ? "80px" : 0,
            }}
          >
            {navItems.map((item, i) => (
              <ListItem
                key={item}
                sx={{
                  mt: 2,
                  transform: menuOpen
                    ? "scale(1) translateY(0px)"
                    : "scale(1.1) translateY(-30px)",
                  opacity: menuOpen ? 1 : 0,
                  transition: "transform 0.5s ease, opacity 0.5s ease",
                  transitionDelay: `${menuOpen ? 0.3 + i * 0.07 : 0}s`,
                }}
              >
                <ListItemText
                  primary={
                    <Typography
                      component="a"
                      href="#"
                      sx={{
                        color: "#FFF",
                        fontSize: 22,
                        textDecoration: "none",
                        fontWeight: 300,
                        fontFamily: "Ek Mukta, sans-serif",
                      }}
                    >
                      {item}
                    </Typography>
                  }
                />
              </ListItem>
            ))}
          </List>
        </Box>
      )}

      {/* Offset for fixed header */}
      <Box sx={{ height: "80px" }} />
    </>
  );
}
