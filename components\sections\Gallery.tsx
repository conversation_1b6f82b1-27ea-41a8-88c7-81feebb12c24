"use client";
import React, { useState } from "react";
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardMedia,
  Dialog,
  DialogContent,
  IconButton,
  useTheme,
  useMediaQuery,
  Chip,
} from "@mui/material";
import { Close as CloseIcon, ArrowBack, ArrowForward } from "@mui/icons-material";

interface GalleryImage {
  id: string;
  category: "ambiance" | "food" | "customers";
  title: string;
  description: string;
  placeholder: string;
  emoji: string;
}

const Gallery: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const [selectedImage, setSelectedImage] = useState<number | null>(null);

  const galleryImages: GalleryImage[] = [
    {
      id: "ambiance-1",
      category: "ambiance",
      title: "Traditional Afghan Decor",
      description: "Authentic cultural elements meet modern Swiss design",
      placeholder: "🏛️",
      emoji: "🎨",
    },
    {
      id: "food-1",
      category: "food",
      title: "Signature Polo Rice",
      description: "Aromatic basmati rice with tender lamb and spices",
      placeholder: "🍚",
      emoji: "🍽️",
    },
    {
      id: "customers-1",
      category: "customers",
      title: "Happy Families",
      description: "Guests enjoying authentic Afghan hospitality",
      placeholder: "👨‍👩‍👧‍👦",
      emoji: "😊",
    },
    {
      id: "food-2",
      category: "food",
      title: "Fresh Manto Dumplings",
      description: "Hand-made dumplings with traditional yogurt sauce",
      placeholder: "🥟",
      emoji: "👨‍🍳",
    },
    {
      id: "ambiance-2",
      category: "ambiance",
      title: "Cozy Dining Area",
      description: "Warm lighting and comfortable seating for families",
      placeholder: "🕯️",
      emoji: "🏠",
    },
    {
      id: "food-3",
      category: "food",
      title: "Qorma Specialties",
      description: "Rich, slow-cooked stews with fresh vegetables",
      placeholder: "🍲",
      emoji: "🔥",
    },
    {
      id: "customers-2",
      category: "customers",
      title: "Cultural Celebration",
      description: "Guests celebrating special occasions with us",
      placeholder: "🎉",
      emoji: "🎊",
    },
    {
      id: "ambiance-3",
      category: "ambiance",
      title: "Open Kitchen View",
      description: "Watch our chefs prepare authentic dishes",
      placeholder: "👨‍🍳",
      emoji: "🔍",
    },
    {
      id: "food-4",
      category: "food",
      title: "Traditional Tea Service",
      description: "Authentic Afghan tea with sweets and nuts",
      placeholder: "🍵",
      emoji: "🫖",
    },
  ];

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "ambiance":
        return theme.palette.primary.main;
      case "food":
        return theme.palette.afghanBlue.main;
      case "customers":
        return theme.palette.swissRed.main;
      default:
        return theme.palette.grey[500];
    }
  };

  const getCategoryGradient = (category: string) => {
    switch (category) {
      case "ambiance":
        return `linear-gradient(135deg, ${theme.palette.primary[100]} 0%, ${theme.palette.primary[200]} 100%)`;
      case "food":
        return `linear-gradient(135deg, ${theme.palette.afghanBlue[100]} 0%, ${theme.palette.afghanBlue[200]} 100%)`;
      case "customers":
        return `linear-gradient(135deg, ${theme.palette.swissRed[100]} 0%, ${theme.palette.swissRed[200]} 100%)`;
      default:
        return `linear-gradient(135deg, ${theme.palette.grey[100]} 0%, ${theme.palette.grey[200]} 100%)`;
    }
  };

  const handleImageClick = (index: number) => {
    setSelectedImage(index);
  };

  const handleClose = () => {
    setSelectedImage(null);
  };

  const handlePrevious = () => {
    if (selectedImage !== null) {
      setSelectedImage(selectedImage === 0 ? galleryImages.length - 1 : selectedImage - 1);
    }
  };

  const handleNext = () => {
    if (selectedImage !== null) {
      setSelectedImage(selectedImage === galleryImages.length - 1 ? 0 : selectedImage + 1);
    }
  };

  return (
    <Box
      component="section"
      sx={{
        py: { xs: 8, md: 12 },
        position: "relative",
        background: `linear-gradient(180deg, 
          ${theme.palette.background.default} 0%, 
          ${theme.palette.background.paper} 100%)`,
      }}
    >
      {/* Background Pattern */}
      <Box
        sx={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: 0.02,
          backgroundImage: `
            radial-gradient(circle at 20% 30%, ${theme.palette.primary.main} 0%, transparent 50%),
            radial-gradient(circle at 80% 70%, ${theme.palette.afghanBlue.main} 0%, transparent 50%)
          `,
          zIndex: 0,
        }}
      />

      <Container maxWidth="lg" sx={{ position: "relative", zIndex: 1 }}>
        {/* Section Header */}
        <Box sx={{ textAlign: "center", mb: 8 }}>
          <Box
            sx={{
              display: "inline-flex",
              alignItems: "center",
              px: 3,
              py: 1,
              mb: 3,
              borderRadius: "50px",
              background: `linear-gradient(135deg, 
                ${theme.palette.afghanBlue[100]} 0%, 
                ${theme.palette.afghanBlue[50]} 100%)`,
              border: `1px solid ${theme.palette.afghanBlue[200]}`,
              boxShadow: theme.customShadows.alpine,
            }}
          >
            <Typography
              variant="caption"
              sx={{
                fontWeight: 600,
                color: theme.palette.afghanBlue.dark,
                textTransform: "uppercase",
                letterSpacing: "0.1em",
              }}
            >
              📸 Gallery
            </Typography>
          </Box>

          <Typography
            variant="h2"
            sx={{
              mb: 3,
              fontFamily: "'Playfair Display', serif",
              fontWeight: 700,
              color: theme.palette.text.primary,
              lineHeight: 1.2,
            }}
          >
            Experience Our World
          </Typography>

          <Typography
            variant="body1"
            sx={{
              color: theme.palette.text.secondary,
              maxWidth: 600,
              mx: "auto",
              fontSize: "1.1rem",
              lineHeight: 1.7,
            }}
          >
            Discover the warmth of our cultural ambiance, the artistry of our cuisine, 
            and the joy of our satisfied guests
          </Typography>
        </Box>

        {/* Gallery Grid */}
        <Grid container spacing={3}>
          {galleryImages.map((image, index) => (
            <Grid 
              item 
              xs={12} 
              sm={6} 
              md={4} 
              key={image.id}
              sx={{
                ...(index === 0 && { md: 6 }), // First image takes more space
                ...(index === 1 && { md: 6 }), // Second image takes more space
              }}
            >
              <Card
                sx={{
                  position: "relative",
                  borderRadius: 3,
                  overflow: "hidden",
                  cursor: "pointer",
                  height: { 
                    xs: 250, 
                    sm: index < 2 ? 300 : 250,
                    md: index < 2 ? 350 : 280 
                  },
                  background: getCategoryGradient(image.category),
                  border: `1px solid rgba(255, 255, 255, 0.2)`,
                  boxShadow: theme.customShadows.alpine,
                  transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
                  "&:hover": {
                    transform: "translateY(-8px) scale(1.02)",
                    boxShadow: theme.customShadows.ornateHover,
                    "& .gallery-overlay": {
                      opacity: 1,
                    },
                    "& .gallery-emoji": {
                      transform: "scale(1.2)",
                    },
                  },
                }}
                onClick={() => handleImageClick(index)}
              >
                {/* Image Placeholder */}
                <Box
                  sx={{
                    height: "100%",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                    textAlign: "center",
                    p: 3,
                    position: "relative",
                  }}
                >
                  {/* Category Chip */}
                  <Chip
                    label={image.category}
                    size="small"
                    sx={{
                      position: "absolute",
                      top: 16,
                      left: 16,
                      background: getCategoryColor(image.category),
                      color: "white",
                      fontWeight: 600,
                      textTransform: "capitalize",
                      fontSize: "0.75rem",
                    }}
                  />

                  {/* Main Emoji */}
                  <Box
                    sx={{
                      fontSize: { xs: "3rem", md: index < 2 ? "4rem" : "3rem" },
                      mb: 2,
                      filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.1))",
                    }}
                  >
                    {image.placeholder}
                  </Box>

                  {/* Content */}
                  <Typography
                    variant={index < 2 ? "h5" : "h6"}
                    sx={{
                      fontFamily: "'Playfair Display', serif",
                      fontWeight: 600,
                      color: theme.palette.text.primary,
                      mb: 1,
                    }}
                  >
                    {image.title}
                  </Typography>

                  <Typography
                    variant="body2"
                    sx={{
                      color: theme.palette.text.secondary,
                      lineHeight: 1.6,
                      maxWidth: 250,
                    }}
                  >
                    {image.description}
                  </Typography>

                  {/* Hover Overlay */}
                  <Box
                    className="gallery-overlay"
                    sx={{
                      position: "absolute",
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      background: `linear-gradient(135deg, 
                        rgba(0,0,0,0.3) 0%, 
                        rgba(0,0,0,0.1) 100%)`,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      opacity: 0,
                      transition: "opacity 0.3s ease",
                    }}
                  >
                    <Box
                      className="gallery-emoji"
                      sx={{
                        fontSize: "2rem",
                        transition: "transform 0.3s ease",
                      }}
                    >
                      {image.emoji}
                    </Box>
                  </Box>
                </Box>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Lightbox Dialog */}
      <Dialog
        open={selectedImage !== null}
        onClose={handleClose}
        maxWidth="lg"
        fullWidth
        sx={{
          "& .MuiDialog-paper": {
            background: "transparent",
            boxShadow: "none",
            overflow: "visible",
          },
        }}
      >
        <DialogContent
          sx={{
            p: 0,
            position: "relative",
            background: "transparent",
          }}
        >
          {selectedImage !== null && (
            <>
              {/* Close Button */}
              <IconButton
                onClick={handleClose}
                sx={{
                  position: "absolute",
                  top: -60,
                  right: 0,
                  color: "white",
                  background: "rgba(0,0,0,0.5)",
                  "&:hover": {
                    background: "rgba(0,0,0,0.7)",
                  },
                  zIndex: 1,
                }}
              >
                <CloseIcon />
              </IconButton>

              {/* Navigation Buttons */}
              <IconButton
                onClick={handlePrevious}
                sx={{
                  position: "absolute",
                  left: -60,
                  top: "50%",
                  transform: "translateY(-50%)",
                  color: "white",
                  background: "rgba(0,0,0,0.5)",
                  "&:hover": {
                    background: "rgba(0,0,0,0.7)",
                  },
                  zIndex: 1,
                }}
              >
                <ArrowBack />
              </IconButton>

              <IconButton
                onClick={handleNext}
                sx={{
                  position: "absolute",
                  right: -60,
                  top: "50%",
                  transform: "translateY(-50%)",
                  color: "white",
                  background: "rgba(0,0,0,0.5)",
                  "&:hover": {
                    background: "rgba(0,0,0,0.7)",
                  },
                  zIndex: 1,
                }}
              >
                <ArrowForward />
              </IconButton>

              {/* Image Content */}
              <Card
                sx={{
                  borderRadius: 3,
                  overflow: "hidden",
                  background: getCategoryGradient(galleryImages[selectedImage].category),
                  minHeight: 400,
                }}
              >
                <Box
                  sx={{
                    height: 400,
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                    textAlign: "center",
                    p: 4,
                  }}
                >
                  <Box sx={{ fontSize: "6rem", mb: 3 }}>
                    {galleryImages[selectedImage].placeholder}
                  </Box>
                  <Typography
                    variant="h4"
                    sx={{
                      fontFamily: "'Playfair Display', serif",
                      fontWeight: 600,
                      color: theme.palette.text.primary,
                      mb: 2,
                    }}
                  >
                    {galleryImages[selectedImage].title}
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{
                      color: theme.palette.text.secondary,
                      lineHeight: 1.7,
                      maxWidth: 400,
                    }}
                  >
                    {galleryImages[selectedImage].description}
                  </Typography>
                </Box>
              </Card>
            </>
          )}
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default Gallery;
