"use client";
import React from "react";
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  useTheme,
  useMediaQuery,
} from "@mui/material";

interface MenuCategory {
  id: string;
  title: string;
  description: string;
  emoji: string;
  gradient: string;
  shadowColor: string;
}

const MenuHighlights: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  const menuCategories: MenuCategory[] = [
    {
      id: "rice-dishes",
      title: "Rice Dishes",
      description: "Authentic Afghan polo and pilaf varieties with aromatic spices and tender meats",
      emoji: "🍚",
      gradient: `linear-gradient(135deg, ${theme.palette.primary[100]} 0%, ${theme.palette.primary[200]} 100%)`,
      shadowColor: theme.palette.primary.main,
    },
    {
      id: "qorma",
      title: "Qorma",
      description: "Traditional slow-cooked stews with rich flavors and tender vegetables",
      emoji: "🍲",
      gradient: `linear-gradient(135deg, ${theme.palette.afghanBlue[100]} 0%, ${theme.palette.afghanBlue[200]} 100%)`,
      shadowColor: theme.palette.afghanBlue.main,
    },
    {
      id: "starters",
      title: "Starters",
      description: "Fresh appetizers and mezze platters to begin your culinary journey",
      emoji: "🥗",
      gradient: `linear-gradient(135deg, ${theme.palette.swissRed[100]} 0%, ${theme.palette.swissRed[200]} 100%)`,
      shadowColor: theme.palette.swissRed.main,
    },
    {
      id: "drinks",
      title: "Drinks",
      description: "Traditional teas, fresh juices, and modern beverage creations",
      emoji: "🍵",
      gradient: `linear-gradient(135deg, ${theme.palette.warning[100]} 0%, ${theme.palette.warning[200]} 100%)`,
      shadowColor: theme.palette.warning.main,
    },
  ];

  return (
    <Box
      component="section"
      sx={{
        py: { xs: 8, md: 12 },
        position: "relative",
        background: `linear-gradient(180deg, 
          ${theme.palette.background.default} 0%, 
          ${theme.palette.background.paper} 100%)`,
      }}
    >
      {/* Background Decorative Elements */}
      <Box
        sx={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: 0.02,
          backgroundImage: `
            radial-gradient(circle at 25% 25%, ${theme.palette.primary.main} 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, ${theme.palette.afghanBlue.main} 0%, transparent 50%)
          `,
          zIndex: 0,
        }}
      />

      <Container maxWidth="lg" sx={{ position: "relative", zIndex: 1 }}>
        {/* Section Header */}
        <Box sx={{ textAlign: "center", mb: 8 }}>
          {/* Section Badge */}
          <Box
            sx={{
              display: "inline-flex",
              alignItems: "center",
              px: 3,
              py: 1,
              mb: 3,
              borderRadius: "50px",
              background: `linear-gradient(135deg, 
                ${theme.palette.warning[100]} 0%, 
                ${theme.palette.warning[50]} 100%)`,
              border: `1px solid ${theme.palette.warning[200]}`,
              boxShadow: theme.customShadows.menu,
            }}
          >
            <Typography
              variant="caption"
              sx={{
                fontWeight: 600,
                color: theme.palette.warning.dark,
                textTransform: "uppercase",
                letterSpacing: "0.1em",
              }}
            >
              Menu Highlights
            </Typography>
          </Box>

          {/* Main Heading */}
          <Typography
            variant="h2"
            sx={{
              mb: 3,
              fontFamily: "'Playfair Display', serif",
              fontWeight: 700,
              color: theme.palette.text.primary,
              lineHeight: 1.2,
            }}
          >
            Discover Our Signature Dishes
          </Typography>

          <Typography
            variant="body1"
            sx={{
              color: theme.palette.text.secondary,
              maxWidth: 600,
              mx: "auto",
              fontSize: "1.1rem",
              lineHeight: 1.7,
            }}
          >
            Experience the perfect fusion of Afghan tradition and Swiss precision 
            through our carefully curated menu selections
          </Typography>
        </Box>

        {/* Menu Grid */}
        <Grid container spacing={4} sx={{ mb: 6 }}>
          {menuCategories.map((category, index) => (
            <Grid item xs={12} sm={6} md={3} key={category.id}>
              <Card
                sx={{
                  height: "100%",
                  position: "relative",
                  borderRadius: 4,
                  overflow: "hidden",
                  background: category.gradient,
                  border: `1px solid rgba(255, 255, 255, 0.2)`,
                  boxShadow: `0 8px 32px rgba(${category.shadowColor.replace('#', '').match(/.{2}/g)?.map(hex => parseInt(hex, 16)).join(', ')}, 0.15)`,
                  transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                  cursor: "pointer",
                  "&:hover": {
                    transform: "translateY(-8px)",
                    boxShadow: `0 16px 48px rgba(${category.shadowColor.replace('#', '').match(/.{2}/g)?.map(hex => parseInt(hex, 16)).join(', ')}, 0.25)`,
                    "& .menu-emoji": {
                      transform: "scale(1.1) rotate(5deg)",
                    },
                    "& .menu-content": {
                      transform: "translateY(-4px)",
                    },
                  },
                }}
              >
                <CardContent
                  sx={{
                    height: "100%",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    textAlign: "center",
                    p: 4,
                    position: "relative",
                  }}
                >
                  {/* Decorative Background Circle */}
                  <Box
                    sx={{
                      position: "absolute",
                      top: -20,
                      right: -20,
                      width: 80,
                      height: 80,
                      borderRadius: "50%",
                      background: `rgba(255, 255, 255, 0.1)`,
                      filter: "blur(20px)",
                    }}
                  />

                  {/* Emoji Icon */}
                  <Box
                    className="menu-emoji"
                    sx={{
                      fontSize: "3rem",
                      mb: 3,
                      transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                      filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.1))",
                    }}
                  >
                    {category.emoji}
                  </Box>

                  {/* Content */}
                  <Box
                    className="menu-content"
                    sx={{
                      transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                      flex: 1,
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "center",
                    }}
                  >
                    <Typography
                      variant="h5"
                      sx={{
                        fontFamily: "'Playfair Display', serif",
                        fontWeight: 600,
                        color: theme.palette.text.primary,
                        mb: 2,
                        lineHeight: 1.3,
                      }}
                    >
                      {category.title}
                    </Typography>

                    <Typography
                      variant="body2"
                      sx={{
                        color: theme.palette.text.secondary,
                        lineHeight: 1.6,
                        fontSize: "0.95rem",
                      }}
                    >
                      {category.description}
                    </Typography>
                  </Box>

                  {/* Floating Badge */}
                  <Box
                    sx={{
                      position: "absolute",
                      bottom: 16,
                      right: 16,
                      width: 8,
                      height: 8,
                      borderRadius: "50%",
                      background: theme.palette.text.primary,
                      opacity: 0.3,
                    }}
                  />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* CTA Section */}
        <Box sx={{ textAlign: "center" }}>
          <Button
            variant="contained"
            size="large"
            sx={{
              px: 6,
              py: 2,
              borderRadius: "50px",
              fontSize: "1.1rem",
              fontWeight: 600,
              textTransform: "none",
              background: `linear-gradient(135deg, 
                ${theme.palette.secondary.main} 0%, 
                ${theme.palette.text.primary} 100%)`,
              color: theme.palette.secondary.contrastText,
              boxShadow: theme.customShadows.ornate,
              transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
              "&:hover": {
                transform: "translateY(-2px)",
                boxShadow: theme.customShadows.ornateHover,
                background: `linear-gradient(135deg, 
                  ${theme.palette.text.primary} 0%, 
                  ${theme.palette.secondary.main} 100%)`,
              },
            }}
          >
            View Full Menu
          </Button>

          {/* Subtitle */}
          <Typography
            variant="body2"
            sx={{
              mt: 2,
              color: theme.palette.text.secondary,
              fontStyle: "italic",
            }}
          >
            Explore our complete collection of Swiss-Afghan fusion cuisine
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default MenuHighlights;
