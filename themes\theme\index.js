// ==============================|| PRESET THEME - THEME SELECTOR ||============================== //

const Theme = (colors) => {
  const { red, gold, cyan, green, grey } = colors;

  // Swiss-Afghan Restaurant Primary Color (Light Green Theme)
  const primaryColor = {
    50: "#FEFFFC",   // Almost white with green tint
    100: "#F9FFEF",  // Very light green
    200: "#F6FFDF",  // Light green
    300: "#F3FFCF",  // Main theme color
    400: "#EFFFAF",  // Slightly darker
    500: "#F3FFCF",  // Primary theme color
    600: "#E8F5BA",  // Darker variant
    700: "#DDEAA5",  // Even darker
    800: "#D2DF90",  // Deep variant
    900: "#C7D47B",  // Darkest variant
    A100: "#F9FFEF", // Accent light
    A200: "#F6FFDF", // Accent medium
    A400: "#EFFFAF", // Accent strong
    A700: "#DDEAA5", // Accent dark
    contrastText: "#000000", // Black text for contrast
  };

  // Swiss-Afghan Secondary Color (Elegant Black for buttons)
  const secondaryColor = {
    50: "#F8F8F8",   // Very light grey
    100: "#F0F0F0",  // Light grey
    200: "#E0E0E0",  // Medium light grey
    300: "#C0C0C0",  // Medium grey
    400: "#808080",  // Dark grey
    500: "#000000",  // Pure black (secondary)
    600: "#1A1A1A",  // Slightly lighter black
    700: "#333333",  // Dark grey
    800: "#4D4D4D",  // Medium dark grey
    900: "#666666",  // Light dark grey
    A100: "#F0F0F0", // Accent light
    A200: "#E0E0E0", // Accent medium
    A400: "#808080", // Accent strong
    A700: "#333333", // Accent dark
    contrastText: "#FFFFFF", // White text for contrast
  };
  
  const greyColors = {
    0: grey[0],
    50: grey[1],
    100: grey[2],
    200: grey[3],
    300: grey[4],
    400: grey[5],
    500: grey[6],
    600: grey[7],
    700: grey[8],
    800: grey[9],
    900: grey[10],
    A50: grey[15],
    A100: grey[11],
    A200: grey[12],
    A400: grey[13],
    A700: grey[14],
    A800: grey[16]
  };
  const contrastText = "#fff";

  // Swiss-Afghan Restaurant Accent Colors
  const swissRedColor = {
    50: "#FFF5F5",   // Very light Swiss red
    100: "#FFE0E0",  // Light red
    200: "#FFB3B3",  // Soft red
    300: "#FF8080",  // Medium red
    400: "#FF4D4D",  // Bright red
    500: "#DC143C",  // Swiss flag red
    600: "#B91C3C",  // Darker red
    700: "#991B1B",  // Deep red
    800: "#7F1D1D",  // Very deep red
    900: "#651E1E",  // Darkest red
    contrastText: "#FFFFFF"
  };

  const afghanBlueColor = {
    50: "#F0F4FF",   // Very light blue
    100: "#E0E7FF",  // Light blue
    200: "#C7D2FE",  // Soft blue
    300: "#A5B4FC",  // Medium blue
    400: "#818CF8",  // Bright blue
    500: "#1E3A8A",  // Deep Afghan blue
    600: "#1E40AF",  // Darker blue
    700: "#1D4ED8",  // Rich blue
    800: "#1E3A8A",  // Deep blue
    900: "#172554",  // Darkest blue
    contrastText: "#FFFFFF"
  };

  return {
    primary: {
      lighter: primaryColor[50],
      100: primaryColor[100],
      200: primaryColor[200],
      light: primaryColor[300],
      400: primaryColor[400],
      main: primaryColor[500],
      dark: primaryColor[600],
      700: primaryColor[700],
      darker: primaryColor[800],
      900: primaryColor[900],
      contrastText: primaryColor.contrastText
    },
    secondary: {
      lighter: secondaryColor[50],
      100: secondaryColor[100],
      200: secondaryColor[200],
      light: secondaryColor[300],
      400: secondaryColor[400],
      main: secondaryColor[500],
      600: secondaryColor[600],
      dark: secondaryColor[600],
      800: secondaryColor[800],
      darker: secondaryColor[900],
      A100: secondaryColor[50],
      A200: secondaryColor[200],
      A400: secondaryColor[400],
      A700: secondaryColor[700],
      contrastText: secondaryColor.contrastText
    },
    // Swiss-Afghan Restaurant Accent Colors
    swissRed: {
      lighter: swissRedColor[50],
      light: swissRedColor[300],
      main: swissRedColor[500],
      dark: swissRedColor[700],
      darker: swissRedColor[900],
      contrastText: swissRedColor.contrastText
    },
    afghanBlue: {
      lighter: afghanBlueColor[50],
      light: afghanBlueColor[300],
      main: afghanBlueColor[500],
      dark: afghanBlueColor[700],
      darker: afghanBlueColor[900],
      contrastText: afghanBlueColor.contrastText
    },
    error: {
      lighter: red[0],
      light: red[2],
      main: red[4],
      dark: red[7],
      darker: red[9],
      contrastText
    },
    warning: {
      lighter: gold[0],
      light: gold[3],
      main: gold[5],
      dark: gold[7],
      darker: gold[9],
      contrastText: greyColors[100]
    },
    info: {
      lighter: cyan[0],
      light: cyan[3],
      main: cyan[5],
      dark: cyan[7],
      darker: cyan[9],
      contrastText
    },
    success: {
      lighter: green[0],
      light: green[3],
      main: green[5],
      dark: green[7],
      darker: green[9],
      contrastText
    },
    grey: greyColors
  };
};

export default Theme;
