"use client";
import React from "react";
import { Box, Typography, Button, Container } from "@mui/material";

const LiquidGlassDemo = () => {
  return (
    <Container maxWidth="lg" sx={{ py: 6 }}>
      <Typography 
        variant="h2" 
        sx={{ 
          textAlign: "center", 
          mb: 6, 
          color: "#000000",
          fontWeight: 700 
        }}
      >
        Liquid Glass Effects Demo
      </Typography>

      <Box sx={{ display: "grid", gridTemplateColumns: { xs: "1fr", md: "1fr 1fr" }, gap: 4 }}>
        
        {/* Glass Card 1 */}
        <Box className="glass-container glass-container--large">
          <div className="glass-filter"></div>
          <div className="glass-overlay"></div>
          <div className="glass-specular"></div>
          <div className="glass-content">
            <Box sx={{ textAlign: "center", py: 4 }}>
              <Typography variant="h4" sx={{ fontWeight: 600, color: "#000000", mb: 2 }}>
                🍕 Menu Item
              </Typography>
              <Typography variant="body1" sx={{ color: "#595958", mb: 3 }}>
                Delicious pizza with modern presentation and liquid glass styling.
              </Typography>
              <Button variant="contained" color="primary">
                Order Now
              </Button>
            </Box>
          </div>
        </Box>

        {/* Glass Card 2 */}
        <Box className="glass-container glass-container--large">
          <div className="glass-filter"></div>
          <div className="glass-overlay"></div>
          <div className="glass-specular"></div>
          <div className="glass-content">
            <Box sx={{ textAlign: "center", py: 4 }}>
              <Typography variant="h4" sx={{ fontWeight: 600, color: "#000000", mb: 2 }}>
                ☕ Beverages
              </Typography>
              <Typography variant="body1" sx={{ color: "#595958", mb: 3 }}>
                Fresh drinks with a modern twist and glassmorphism design.
              </Typography>
              <Button variant="outlined" color="primary">
                View Menu
              </Button>
            </Box>
          </div>
        </Box>

        {/* Glass Navigation */}
        <Box 
          className="glass-container glass-container--rounded" 
          sx={{ gridColumn: { xs: "1", md: "1 / -1" }, mt: 4 }}
        >
          <div className="glass-filter"></div>
          <div className="glass-overlay"></div>
          <div className="glass-specular"></div>
          <div className="glass-content">
            <Box sx={{ display: "flex", justifyContent: "space-around", alignItems: "center", py: 2 }}>
              <div className="glass-item">
                <Typography variant="body2" sx={{ fontWeight: 500 }}>🏠</Typography>
                <Typography variant="caption">Home</Typography>
              </div>
              <div className="glass-item glass-item--active">
                <Typography variant="body2" sx={{ fontWeight: 500 }}>🍽️</Typography>
                <Typography variant="caption">Menu</Typography>
              </div>
              <div className="glass-item">
                <Typography variant="body2" sx={{ fontWeight: 500 }}>📞</Typography>
                <Typography variant="caption">Contact</Typography>
              </div>
              <div className="glass-item">
                <Typography variant="body2" sx={{ fontWeight: 500 }}>ℹ️</Typography>
                <Typography variant="caption">About</Typography>
              </div>
            </Box>
          </div>
        </Box>

        {/* Small Glass Elements */}
        <Box sx={{ display: "flex", gap: 2, gridColumn: { xs: "1", md: "1 / -1" }, mt: 2 }}>
          <Box className="glass-container glass-container--small">
            <div className="glass-filter"></div>
            <div className="glass-overlay"></div>
            <div className="glass-specular"></div>
            <div className="glass-content">
              <Typography variant="body2" sx={{ fontWeight: 600, color: "#000000" }}>
                ⭐ 4.9 Rating
              </Typography>
            </div>
          </Box>

          <Box className="glass-container glass-container--small">
            <div className="glass-filter"></div>
            <div className="glass-overlay"></div>
            <div className="glass-specular"></div>
            <div className="glass-content">
              <Typography variant="body2" sx={{ fontWeight: 600, color: "#000000" }}>
                🚚 Free Delivery
              </Typography>
            </div>
          </Box>

          <Box className="glass-container glass-container--small">
            <div className="glass-filter"></div>
            <div className="glass-overlay"></div>
            <div className="glass-specular"></div>
            <div className="glass-content">
              <Typography variant="body2" sx={{ fontWeight: 600, color: "#000000" }}>
                ⏰ 30 min
              </Typography>
            </div>
          </Box>
        </Box>

      </Box>

      <Box sx={{ mt: 6, textAlign: "center" }}>
        <Typography variant="h5" sx={{ color: "#000000", mb: 2 }}>
          Theme Colors Applied
        </Typography>
        <Typography variant="body1" sx={{ color: "#595958", mb: 3 }}>
          Background: #F3FFCF (Light Green) | Buttons: Black with animations | Glass effects for modern cards
        </Typography>
        <Box sx={{ display: "flex", justifyContent: "center", gap: 2 }}>
          <Button variant="contained" color="primary">
            Primary Button (Black)
          </Button>
          <Button variant="contained" color="secondary">
            Secondary Button (Theme)
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default LiquidGlassDemo;
