// ==============================|| OVERRIDES - BUTTON ||============================== //

export default function Button(theme) {
  const disabledStyle = {
    "&.Mui-disabled": {
      backgroundColor: theme.palette.grey[200],
      opacity: 0.6,
      transform: "none",
      boxShadow: "none"
    }
  };



  return {
    MuiButton: {
      defaultProps: {
        disableElevation: false,
        disableRipple: false,
      },
      styleOverrides: {
        root: {
          fontWeight: 500,
          fontSize: "16px",
          borderRadius: "25px",
          letterSpacing: "0.2px",
          textTransform: "none",
          padding: "12px 24px",
          position: "relative",
          transition: "all 0.3s ease",
          boxShadow: "none",

          // "&:hover": {
          //   transform: "translateY(-1px)",
          //   boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
          // },

          "&:active": {
            transform: "translateY(0)",
            transition: "all 0.1s ease",
          }
        },

        // Modern Primary Button (Black with animations)
        containedPrimary: {
          backgroundColor: "#000000",
          color: "#FFFFFF",
          position: "relative",
          overflow: "hidden",

          "&::before": {
            content: '""',
            position: "absolute",
            top: 0,
            left: "-100%",
            width: "100%",
            height: "100%",
            background: "linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)",
            transition: "left 0.5s ease",
          },

          "&:hover": {
            backgroundColor: "#1A1A1A",
            boxShadow: "0 8px 25px rgba(0, 0, 0, 0.3)",
            transform: "translateY(-2px)",

            "&::before": {
              left: "100%",
            }
          },

          "&:active": {
            backgroundColor: "#000000",
            transform: "translateY(0)",
          }
        },

        // Light Theme Secondary Button
        containedSecondary: {
          backgroundColor: theme.palette.primary.main,
          color: "#000000",
          border: "1px solid rgba(0, 0, 0, 0.1)",

          "&:hover": {
            backgroundColor: theme.palette.primary[400],
            boxShadow: "0 6px 20px rgba(243, 255, 207, 0.4)",
            transform: "translateY(-1px)",
          }
        },

        // Modern Outlined Button
        outlined: {
          border: "1px solid #000000",
          color: "#000000",
          backgroundColor: "transparent",

          "&:hover": {
            backgroundColor: "#000000",
            color: "#FFFFFF",
            borderColor: "#000000",
            transform: "translateY(-1px)",
          }
        },

        // Modern Text Button
        text: {
          color: "#000000",
          position: "relative",
          overflow: "hidden",
          letterSpacing: "0.2px",
          transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",

          "&::before": {
            content: '""',
            position: "absolute",
            top: "50%",
            left: "-30px",
            width: "20px",
            height: "25px",
            borderTopRightRadius: "15px",
            borderBottomRightRadius: "15px",
            transform: "translateY(-50%)",
            backgroundColor: theme.palette.primary.dark,
            opacity: 0,
            transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
          },

          "&:hover": {
            letterSpacing: "0.05px",
            // paddingLeft: "30px",

            "&::before": {
              left: "-5px",
              opacity: 1,
            }
          }
        },

        // Clean size variants
        sizeSmall: {
          padding: "8px 16px",
          fontSize: "14px",
          borderRadius: "20px",
        },

        sizeMedium: {
          padding: "12px 24px",
          fontSize: "16px",
          borderRadius: "25px",
        },

        sizeLarge: {
          padding: "16px 32px",
          fontSize: "16px",
          borderRadius: "25px",
          fontWeight: 600,
        },

        // Disabled state styling
        "&.Mui-disabled": disabledStyle["&.Mui-disabled"]
      }
    }
  };
}
