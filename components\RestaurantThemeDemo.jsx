"use client";
import React from "react";
import {
  Box,
  Typo<PERSON>,
  But<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Container,
  Rating,
} from "@mui/material";

const RestaurantThemeDemo = () => {
  return (
    <Box sx={{ minHeight: "100vh" }}>
      {/* Navigation Bar */}
      {/* <AppBar
        position="static"
        elevation={0}
        sx={{
          backgroundColor: "transparent",
          borderBottom: "none",
          py: 1,
        }}
      >
        <Toolbar sx={{ justifyContent: "space-between", px: { xs: 2, md: 4 } }}>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Box
              sx={{
                width: 40,
                height: 40,
                borderRadius: "50%",
                backgroundColor: "#000000",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                mr: 2,
              }}
            >
              <Typography variant="h6" sx={{ color: "white", fontWeight: 700 }}>
                AK
              </Typography>
            </Box>
            <Typography
              variant="h6"
              sx={{
                color: "#000000",
                fontWeight: 600,
                fontSize: "18px",
              }}
            >
              Alpen Kabul
            </Typography>
          </Box>

          <Box sx={{ display: { xs: "none", md: "flex" }, gap: 4 }}>
            <Typography
              variant="body1"
              sx={{
                color: "#2D5D3F",
                cursor: "pointer",
                "&:hover": { color: "#D4941A" },
                transition: "color 0.3s ease",
              }}
            >
              Menu
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: "#2D5D3F",
                cursor: "pointer",
                "&:hover": { color: "#D4941A" },
                transition: "color 0.3s ease",
              }}
            >
              News
            </Typography>
          </Box>
          <Button
            variant="contained"
            sx={{
              backgroundColor: "#2D5D3F",
              color: "white",
              borderRadius: "25px",
              px: 3,
              py: 1,
              textTransform: "none",
              fontWeight: 500,
              "&:hover": {
                backgroundColor: "#1D402B",
                transform: "translateY(-1px)",
              },
              transition: "all 0.3s ease",
            }}
          >
            Book table
          </Button>
        </Toolbar>
      </AppBar> */}

      {/* Hero Section */}
      <Container maxWidth="xl" sx={{ px: { xs: 2, md: 4 }, py: 6 }}>
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: { xs: "1fr", lg: "1fr 1fr" },
            gap: 6,
            alignItems: "center",
            minHeight: "70vh",
          }}
        >
          {/* Left Content */}
          <Box sx={{ order: { xs: 2, lg: 1 } }}>
            {/* Rating Badge with Liquid Glass Effect */}
            <Box
              className="glass-container glass-container--small"
              sx={{ mb: 4, width: "fit-content" }}
            >
              <div className="glass-filter"></div>
              <div className="glass-overlay"></div>
              <div className="glass-specular"></div>
              <div className="glass-content">
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography
                    variant="body2"
                    sx={{ fontWeight: 500, color: "#000000" }}
                  >
                    Uber Eats:
                  </Typography>
                  <Rating value={4.9} precision={0.1} size="small" readOnly />
                  <Typography
                    variant="body2"
                    sx={{ fontWeight: 600, color: "#000000" }}
                  >
                    (4.9)
                  </Typography>
                </Box>
              </div>
            </Box>

            {/* Main Heading */}
            <Typography
              variant="h1"
              sx={{
                fontSize: { xs: "2.5rem", md: "3.5rem", lg: "4rem" },
                fontWeight: 700,
                color: "#000000",
                lineHeight: 1.1,
                mb: 3,
                fontFamily: "'Playfair Display', serif",
              }}
            >
              Bite into{" "}
              <Box component="span" sx={{ color: "#000000", fontWeight: 800 }}>
                Happiness
              </Box>
            </Typography>

            {/* Subtitle */}
            <Typography
              variant="body1"
              sx={{
                fontSize: "18px",
                color: "#595958",
                mb: 4,
                lineHeight: 1.6,
                maxWidth: "500px",
              }}
            >
              Welcome to Burger Haven, where every bite is a step closer to
              happiness.
            </Typography>

            {/* Action Buttons */}
            <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}>
              <Button variant="contained" color="primary" size="large">
                Book table
              </Button>
              <Button variant="outlined" color="primary" size="large">
                Explore menu
              </Button>
            </Box>

            {/* Liquid Glass Feature Card */}
            <Box
              className="glass-container glass-container--medium"
              sx={{ mt: 4, maxWidth: "400px" }}
            >
              <div className="glass-filter"></div>
              <div className="glass-overlay"></div>
              <div className="glass-specular"></div>
              <div className="glass-content">
                <Box sx={{ textAlign: "center", py: 2 }}>
                  <Typography
                    variant="h6"
                    sx={{ fontWeight: 600, color: "#000000", mb: 1 }}
                  >
                    🍔 Today's Special
                  </Typography>
                  <Typography variant="body2" sx={{ color: "#595958", mb: 2 }}>
                    Swiss Alpine Burger with Afghan spices
                  </Typography>
                  <Typography
                    variant="h5"
                    sx={{ fontWeight: 700, color: "#000000" }}
                  >
                    $24.99
                  </Typography>
                </Box>
              </div>
            </Box>
          </Box>

          {/* Right Image */}
          <Box sx={{ order: { xs: 1, lg: 2 }, position: "relative" }}>
            <Box
              sx={{
                width: "100%",
                height: { xs: "300px", md: "500px" },
                borderRadius: "24px",
                background: `
                  linear-gradient(135deg, rgba(45, 93, 63, 0.1) 0%, rgba(212, 148, 26, 0.1) 100%),
                  url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 300"><rect width="400" height="300" fill="%23f5f5f4"/><circle cx="200" cy="150" r="80" fill="%232D5D3F" opacity="0.1"/><circle cx="150" cy="100" r="40" fill="%23D4941A" opacity="0.2"/><circle cx="250" cy="200" r="60" fill="%232D5D3F" opacity="0.15"/><text x="200" y="160" text-anchor="middle" fill="%232D5D3F" font-size="24" font-weight="bold">Afghan Cuisine</text></svg>')
                `,
                backgroundSize: "cover",
                backgroundPosition: "center",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                position: "relative",
                overflow: "hidden",
                "&::before": {
                  content: '""',
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: `
                    radial-gradient(circle at 30% 30%, rgba(212, 148, 26, 0.1) 2px, transparent 2px),
                    radial-gradient(circle at 70% 70%, rgba(45, 93, 63, 0.1) 2px, transparent 2px)
                  `,
                  backgroundSize: "40px 40px, 60px 60px",
                  opacity: 0.6,
                  animation: "afghanPattern 20s ease-in-out infinite",
                },
              }}
            >
              {/* Placeholder for food image */}
              <Box
                sx={{
                  position: "relative",
                  zIndex: 2,
                  textAlign: "center",
                  color: "#2D5D3F",
                }}
              >
                <Typography
                  variant="h3"
                  sx={{
                    fontWeight: 700,
                    mb: 2,
                    fontFamily: "'Playfair Display', serif",
                  }}
                >
                  🍽️
                </Typography>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Exquisite Fusion Cuisine
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8, mt: 1 }}>
                  Swiss precision meets Afghan tradition
                </Typography>
              </Box>
            </Box>

            {/* Floating Badge with Liquid Glass */}
            <Box
              className="glass-container glass-container--small"
              sx={{
                position: "absolute",
                bottom: 20,
                right: 20,
                width: "auto",
              }}
            >
              <div className="glass-filter"></div>
              <div className="glass-overlay"></div>
              <div className="glass-specular"></div>
              <div className="glass-content">
                <Typography
                  variant="caption"
                  sx={{ color: "#000000", fontWeight: 600 }}
                >
                  Modern Restaurant Theme
                </Typography>
              </div>
            </Box>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default RestaurantThemeDemo;
