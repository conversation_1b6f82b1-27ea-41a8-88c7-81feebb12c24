"use client";
import React from "react";
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  useTheme,
  useMediaQuery,
} from "@mui/material";

interface SpecialDish {
  id: string;
  name: string;
  nameEnglish: string;
  description: string;
  emoji: string;
  price: string;
  gradient: string;
}

const WeekendSpecials: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  const specialDishes: SpecialDish[] = [
    {
      id: "manto",
      name: "منتو",
      nameEnglish: "Manto",
      description: "Traditional Afghan steamed dumplings filled with seasoned meat, served with yogurt and lentil sauce",
      emoji: "🥟",
      price: "$18.99",
      gradient: `linear-gradient(135deg, ${theme.palette.primary[100]} 0%, ${theme.palette.primary[200]} 100%)`,
    },
    {
      id: "ashak",
      name: "آشک",
      nameEnglish: "Ashak",
      description: "Delicate leek-filled dumplings topped with meat sauce, yogurt, and dried mint",
      emoji: "🥙",
      price: "$16.99",
      gradient: `linear-gradient(135deg, ${theme.palette.afghanBlue[100]} 0%, ${theme.palette.afghanBlue[200]} 100%)`,
    },
    {
      id: "bolani",
      name: "بولانی",
      nameEnglish: "Bolani",
      description: "Crispy flatbread stuffed with potatoes, herbs, and spices, served with fresh chutney",
      emoji: "🫓",
      price: "$12.99",
      gradient: `linear-gradient(135deg, ${theme.palette.swissRed[100]} 0%, ${theme.palette.swissRed[200]} 100%)`,
    },
  ];

  return (
    <Box
      component="section"
      sx={{
        py: { xs: 8, md: 12 },
        position: "relative",
        background: `linear-gradient(135deg, 
          ${theme.palette.background.paper} 0%, 
          ${theme.palette.primary[50]} 100%)`,
        overflow: "hidden",
      }}
    >
      {/* Decorative Background Elements */}
      <Box
        sx={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: 0.03,
          backgroundImage: `
            radial-gradient(circle at 30% 20%, ${theme.palette.afghanBlue.main} 0%, transparent 50%),
            radial-gradient(circle at 70% 80%, ${theme.palette.swissRed.main} 0%, transparent 50%)
          `,
          zIndex: 0,
        }}
      />

      {/* Traditional Pattern Overlay */}
      <Box
        sx={{
          position: "absolute",
          top: "15%",
          left: "10%",
          width: 150,
          height: 150,
          opacity: 0.04,
          background: `conic-gradient(from 45deg, 
            ${theme.palette.afghanBlue.main}, 
            ${theme.palette.primary.main}, 
            ${theme.palette.swissRed.main}, 
            ${theme.palette.afghanBlue.main})`,
          borderRadius: "50%",
          filter: "blur(2px)",
          zIndex: 0,
        }}
      />

      <Container maxWidth="lg" sx={{ position: "relative", zIndex: 1 }}>
        {/* Section Header */}
        <Box sx={{ textAlign: "center", mb: 8 }}>
          {/* Weekend Badge */}
          <Box
            sx={{
              display: "inline-flex",
              alignItems: "center",
              px: 4,
              py: 1.5,
              mb: 3,
              borderRadius: "50px",
              background: `linear-gradient(135deg, 
                ${theme.palette.swissRed[100]} 0%, 
                ${theme.palette.swissRed[50]} 100%)`,
              border: `2px solid ${theme.palette.swissRed[200]}`,
              boxShadow: theme.customShadows.ornate,
              position: "relative",
              overflow: "hidden",
              "&::before": {
                content: '""',
                position: "absolute",
                top: 0,
                left: "-100%",
                width: "100%",
                height: "100%",
                background: `linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)`,
                animation: "shimmer 3s infinite",
              },
              "@keyframes shimmer": {
                "0%": { left: "-100%" },
                "100%": { left: "100%" },
              },
            }}
          >
            <Typography
              variant="body1"
              sx={{
                fontWeight: 700,
                color: theme.palette.swissRed.dark,
                textTransform: "uppercase",
                letterSpacing: "0.1em",
                fontSize: "0.9rem",
              }}
            >
              🗓️ Only Available Friday–Sunday
            </Typography>
          </Box>

          {/* Main Heading */}
          <Typography
            variant="h2"
            sx={{
              mb: 3,
              fontFamily: "'Playfair Display', serif",
              fontWeight: 700,
              color: theme.palette.text.primary,
              lineHeight: 1.2,
            }}
          >
            Weekend Specials
          </Typography>

          <Typography
            variant="h4"
            sx={{
              mb: 4,
              fontFamily: "'Playfair Display', serif",
              fontWeight: 400,
              color: theme.palette.afghanBlue.main,
              fontSize: { xs: "1.5rem", md: "2rem" },
              direction: "rtl",
              textAlign: "center",
            }}
          >
            منتو، آشک، بولانی
          </Typography>

          <Typography
            variant="body1"
            sx={{
              color: theme.palette.text.secondary,
              maxWidth: 700,
              mx: "auto",
              fontSize: "1.1rem",
              lineHeight: 1.7,
            }}
          >
            Experience authentic Afghan comfort food, lovingly prepared using traditional family recipes. 
            These special dishes require extra preparation time and are exclusively available on weekends.
          </Typography>
        </Box>

        {/* Specials Grid */}
        <Grid container spacing={4} sx={{ mb: 6 }}>
          {specialDishes.map((dish, index) => (
            <Grid item xs={12} md={4} key={dish.id}>
              <Card
                sx={{
                  height: "100%",
                  position: "relative",
                  borderRadius: 4,
                  overflow: "hidden",
                  background: dish.gradient,
                  border: `1px solid rgba(255, 255, 255, 0.3)`,
                  boxShadow: theme.customShadows.warm,
                  transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
                  cursor: "pointer",
                  "&:hover": {
                    transform: "translateY(-12px) scale(1.02)",
                    boxShadow: theme.customShadows.ornateHover,
                    "& .dish-emoji": {
                      transform: "scale(1.2) rotate(10deg)",
                    },
                    "& .dish-content": {
                      transform: "translateY(-8px)",
                    },
                    "& .price-badge": {
                      transform: "scale(1.1)",
                      background: theme.palette.warning.main,
                    },
                  },
                }}
              >
                <CardContent
                  sx={{
                    height: "100%",
                    display: "flex",
                    flexDirection: "column",
                    p: 4,
                    position: "relative",
                  }}
                >
                  {/* Price Badge */}
                  <Box
                    className="price-badge"
                    sx={{
                      position: "absolute",
                      top: 20,
                      right: 20,
                      px: 2,
                      py: 1,
                      borderRadius: "20px",
                      background: theme.palette.warning.light,
                      color: theme.palette.warning.contrastText,
                      fontWeight: 700,
                      fontSize: "1rem",
                      boxShadow: theme.customShadows.glass,
                      transition: "all 0.3s ease",
                    }}
                  >
                    {dish.price}
                  </Box>

                  {/* Emoji Icon */}
                  <Box
                    className="dish-emoji"
                    sx={{
                      fontSize: "4rem",
                      textAlign: "center",
                      mb: 3,
                      transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
                      filter: "drop-shadow(0 6px 12px rgba(0,0,0,0.15))",
                    }}
                  >
                    {dish.emoji}
                  </Box>

                  {/* Content */}
                  <Box
                    className="dish-content"
                    sx={{
                      transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
                      flex: 1,
                      textAlign: "center",
                    }}
                  >
                    {/* Afghan Name */}
                    <Typography
                      variant="h4"
                      sx={{
                        fontFamily: "'Playfair Display', serif",
                        fontWeight: 600,
                        color: theme.palette.afghanBlue.main,
                        mb: 1,
                        fontSize: { xs: "1.8rem", md: "2rem" },
                        direction: "rtl",
                      }}
                    >
                      {dish.name}
                    </Typography>

                    {/* English Name */}
                    <Typography
                      variant="h6"
                      sx={{
                        fontFamily: "'Playfair Display', serif",
                        fontWeight: 500,
                        color: theme.palette.text.primary,
                        mb: 3,
                        fontSize: "1.2rem",
                      }}
                    >
                      {dish.nameEnglish}
                    </Typography>

                    {/* Description */}
                    <Typography
                      variant="body2"
                      sx={{
                        color: theme.palette.text.secondary,
                        lineHeight: 1.7,
                        fontSize: "1rem",
                        mb: 3,
                      }}
                    >
                      {dish.description}
                    </Typography>

                    {/* Weekend Only Chip */}
                    <Chip
                      label="Weekend Only"
                      size="small"
                      sx={{
                        background: `linear-gradient(135deg, 
                          ${theme.palette.swissRed.light} 0%, 
                          ${theme.palette.swissRed.main} 100%)`,
                        color: theme.palette.swissRed.contrastText,
                        fontWeight: 600,
                        fontSize: "0.75rem",
                      }}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* CTA Buttons */}
        <Box 
          sx={{ 
            display: "flex", 
            justifyContent: "center", 
            gap: 3,
            flexWrap: "wrap",
          }}
        >
          <Button
            variant="contained"
            size="large"
            sx={{
              px: 6,
              py: 2,
              borderRadius: "50px",
              fontSize: "1.1rem",
              fontWeight: 600,
              textTransform: "none",
              background: `linear-gradient(135deg, 
                ${theme.palette.afghanBlue.main} 0%, 
                ${theme.palette.afghanBlue.dark} 100%)`,
              color: theme.palette.afghanBlue.contrastText,
              boxShadow: theme.customShadows.ornate,
              transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
              "&:hover": {
                transform: "translateY(-3px)",
                boxShadow: theme.customShadows.ornateHover,
              },
            }}
          >
            🍽️ Pre-Order Now
          </Button>

          <Button
            variant="outlined"
            size="large"
            sx={{
              px: 6,
              py: 2,
              borderRadius: "50px",
              fontSize: "1.1rem",
              fontWeight: 600,
              textTransform: "none",
              borderColor: theme.palette.swissRed.main,
              color: theme.palette.swissRed.main,
              borderWidth: 2,
              transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
              "&:hover": {
                transform: "translateY(-3px)",
                background: theme.palette.swissRed.main,
                color: theme.palette.swissRed.contrastText,
                borderColor: theme.palette.swissRed.main,
                boxShadow: theme.customShadows.warm,
              },
            }}
          >
            📅 Reserve Table
          </Button>
        </Box>
      </Container>
    </Box>
  );
};

export default WeekendSpecials;
