"use client";
import React from "react";
import {
  Box,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Avatar,
  Rating,
  useTheme,
  useMediaQuery,
} from "@mui/material";

interface Testimonial {
  id: string;
  name: string;
  location: string;
  rating: number;
  review: string;
  platform: "Google" | "Yelp" | "TripAdvisor";
  avatar: string;
  date: string;
}

const Testimonials: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  const testimonials: Testimonial[] = [
    {
      id: "1",
      name: "<PERSON>",
      location: "Zurich, Switzerland",
      rating: 5,
      review:
        "Absolutely incredible! The manto dumplings were perfectly seasoned and the service was exceptional. The blend of Afghan and Swiss hospitality creates such a welcoming atmosphere. Will definitely be back!",
      platform: "Google",
      avatar: "👩‍💼",
      date: "2 weeks ago",
    },
    {
      id: "2",
      name: "<PERSON>",
      location: "Geneva, Switzerland",
      rating: 5,
      review:
        "As someone from Afghanistan, I can say this is the most authentic Afghan food I've found in Switzerland. The polo rice reminded me of my grandmother's cooking. Thank you for preserving our culture!",
      platform: "Google",
      avatar: "👨‍🎓",
      date: "1 month ago",
    },
    {
      id: "3",
      name: "<PERSON> L.",
      location: "Basel, Switzerland",
      rating: 5,
      review:
        "The weekend specials are worth the wait! We tried the ashak and bolani - both were outstanding. The restaurant has such beautiful decor that perfectly balances modern and traditional elements.",
      platform: "Google",
      avatar: "👩‍🍳",
      date: "3 weeks ago",
    },
    {
      id: "4",
      name: "Thomas R.",
      location: "Bern, Switzerland",
      rating: 4,
      review:
        "Great introduction to Afghan cuisine! The staff was very helpful in explaining the dishes. The qorma was rich and flavorful. Perfect for a family dinner with something different.",
      platform: "Google",
      avatar: "👨‍👩‍👧‍👦",
      date: "1 week ago",
    },
    {
      id: "5",
      name: "Fatima A.",
      location: "Lausanne, Switzerland",
      rating: 5,
      review:
        "The tea service is exceptional! They serve it the traditional way with sweets and nuts. The atmosphere is so cozy and the food quality is consistently excellent. Highly recommend!",
      platform: "Google",
      avatar: "👩‍🎨",
      date: "2 days ago",
    },
    {
      id: "6",
      name: "David S.",
      location: "St. Gallen, Switzerland",
      rating: 5,
      review:
        "Polo restaurant exceeded all expectations. The fusion of Swiss precision with Afghan flavors is brilliant. The presentation is beautiful and the taste is even better. A true gem!",
      platform: "Google",
      avatar: "👨‍💻",
      date: "4 days ago",
    },
  ];

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case "Google":
        return "#4285F4";
      case "Yelp":
        return "#FF1744";
      case "TripAdvisor":
        return "#00AA6C";
      default:
        return theme.palette.primary.main;
    }
  };

  return (
    <Box
      component="section"
      sx={{
        py: { xs: 8, md: 12 },
        position: "relative",
        background: `linear-gradient(135deg, 
          ${theme.palette.primary[50]} 0%, 
          ${theme.palette.background.paper} 100%)`,
      }}
    >
      {/* Background Elements */}
      <Box
        sx={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: 0.03,
          backgroundImage: `
            radial-gradient(circle at 25% 25%, ${theme.palette.swissRed.main} 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, ${theme.palette.afghanBlue.main} 0%, transparent 50%)
          `,
          zIndex: 0,
        }}
      />

      <Container maxWidth="lg" sx={{ position: "relative", zIndex: 1 }}>
        {/* Section Header */}
        <Box sx={{ textAlign: "center", mb: 8 }}>
          <Box
            sx={{
              display: "inline-flex",
              alignItems: "center",
              px: 3,
              py: 1,
              mb: 3,
              borderRadius: "50px",
              background: `linear-gradient(135deg, 
                ${theme.palette.warning[100]} 0%, 
                ${theme.palette.warning[50]} 100%)`,
              border: `1px solid ${theme.palette.warning[200]}`,
              boxShadow: theme.customShadows.warm,
            }}
          >
            <Typography
              variant="caption"
              sx={{
                fontWeight: 600,
                color: theme.palette.warning.dark,
                textTransform: "uppercase",
                letterSpacing: "0.1em",
              }}
            >
              ⭐ Customer Reviews
            </Typography>
          </Box>

          <Typography
            variant="h2"
            sx={{
              mb: 3,
              fontFamily: "'Playfair Display', serif",
              fontWeight: 700,
              color: theme.palette.text.primary,
              lineHeight: 1.2,
            }}
          >
            What Our Guests Say
          </Typography>

          <Typography
            variant="body1"
            sx={{
              color: theme.palette.text.secondary,
              maxWidth: 600,
              mx: "auto",
              fontSize: "1.1rem",
              lineHeight: 1.7,
            }}
          >
            Real reviews from our valued customers who have experienced the
            warmth of Afghan hospitality and Swiss quality
          </Typography>

          {/* Google Rating Summary */}
          <Box
            sx={{
              display: "inline-flex",
              alignItems: "center",
              gap: 2,
              mt: 4,
              px: 4,
              py: 2,
              borderRadius: "50px",
              background: `rgba(255, 255, 255, 0.8)`,
              backdropFilter: "blur(10px)",
              border: `1px solid rgba(255, 255, 255, 0.3)`,
              boxShadow: theme.customShadows.glass,
            }}
          >
            <Box sx={{ fontSize: "1.5rem" }}>🌟</Box>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 700,
                color: theme.palette.text.primary,
              }}
            >
              4.9
            </Typography>
            <Rating value={4.9} precision={0.1} size="small" readOnly />
            <Typography
              variant="body2"
              sx={{
                color: theme.palette.text.secondary,
                fontWeight: 500,
              }}
            >
              (127 Google Reviews)
            </Typography>
          </Box>
        </Box>

        {/* Testimonials Grid */}
        <Grid container spacing={4}>
          {testimonials.map((testimonial, index) => (
            <Grid item xs={12} md={6} lg={4} key={testimonial.id}>
              <Card
                sx={{
                  height: "100%",
                  position: "relative",
                  borderRadius: 4,
                  overflow: "hidden",
                  background: `linear-gradient(135deg, 
                    rgba(255, 255, 255, 0.9) 0%, 
                    rgba(255, 255, 255, 0.7) 100%)`,
                  backdropFilter: "blur(10px)",
                  border: `1px solid rgba(255, 255, 255, 0.3)`,
                  boxShadow: theme.customShadows.glass,
                  transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                  "&:hover": {
                    transform: "translateY(-8px)",
                    boxShadow: theme.customShadows.glassHover,
                  },
                }}
              >
                <CardContent sx={{ p: 4, height: "100%" }}>
                  {/* Header */}
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      mb: 3,
                    }}
                  >
                    <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                      <Avatar
                        sx={{
                          width: 48,
                          height: 48,
                          background: `linear-gradient(135deg, 
                            ${theme.palette.primary.light} 0%, 
                            ${theme.palette.primary.main} 100%)`,
                          fontSize: "1.5rem",
                        }}
                      >
                        {testimonial.avatar}
                      </Avatar>
                      <Box>
                        <Typography
                          variant="h6"
                          sx={{
                            fontWeight: 600,
                            color: theme.palette.text.primary,
                            fontSize: "1rem",
                          }}
                        >
                          {testimonial.name}
                        </Typography>
                        <Typography
                          variant="caption"
                          sx={{
                            color: theme.palette.text.secondary,
                            fontSize: "0.8rem",
                          }}
                        >
                          {testimonial.location}
                        </Typography>
                      </Box>
                    </Box>

                    {/* Platform Badge */}
                    <Box
                      sx={{
                        px: 2,
                        py: 0.5,
                        borderRadius: "20px",
                        background: getPlatformColor(testimonial.platform),
                        color: "white",
                        fontSize: "0.75rem",
                        fontWeight: 600,
                      }}
                    >
                      {testimonial.platform}
                    </Box>
                  </Box>

                  {/* Rating */}
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 1,
                      mb: 3,
                    }}
                  >
                    <Rating value={testimonial.rating} size="small" readOnly />
                    <Typography
                      variant="caption"
                      sx={{
                        color: theme.palette.text.secondary,
                        fontWeight: 500,
                      }}
                    >
                      {testimonial.date}
                    </Typography>
                  </Box>

                  {/* Review Text */}
                  <Typography
                    variant="body2"
                    sx={{
                      color: theme.palette.text.secondary,
                      lineHeight: 1.7,
                      fontSize: "0.95rem",
                      fontStyle: "italic",
                      position: "relative",
                      "&::before": {
                        content: '"',
                        fontSize: "2rem",
                        color: theme.palette.primary.main,
                        position: "absolute",
                        top: -10,
                        left: -10,
                        fontFamily: "'Playfair Display', serif",
                      },
                      "&::after": {
                        content: '"',
                        fontSize: "2rem",
                        color: theme.palette.primary.main,
                        position: "absolute",
                        bottom: -20,
                        right: 0,
                        fontFamily: "'Playfair Display', serif",
                      },
                      pl: 2,
                      pr: 2,
                    }}
                  >
                    {testimonial.review}
                  </Typography>
                </CardContent>

                {/* Decorative Corner */}
                <Box
                  sx={{
                    position: "absolute",
                    top: 0,
                    right: 0,
                    width: 0,
                    height: 0,
                    borderLeft: "20px solid transparent",
                    borderTop: `20px solid ${theme.palette.primary.main}`,
                    opacity: 0.1,
                  }}
                />
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* View More Reviews CTA */}
        <Box sx={{ textAlign: "center", mt: 6 }}>
          <Typography
            variant="body2"
            sx={{
              color: theme.palette.text.secondary,
              mb: 2,
            }}
          >
            See more reviews on Google
          </Typography>
          <Box
            component="a"
            href="https://www.google.com/maps/place/Polo+Restaurant"
            target="_blank"
            rel="noopener noreferrer"
            sx={{
              display: "inline-flex",
              alignItems: "center",
              gap: 1,
              px: 4,
              py: 2,
              borderRadius: "50px",
              background: "#4285F4",
              color: "white",
              textDecoration: "none",
              fontWeight: 600,
              fontSize: "0.9rem",
              transition: "all 0.3s ease",
              "&:hover": {
                transform: "translateY(-2px)",
                boxShadow: "0 8px 24px rgba(66, 133, 244, 0.3)",
              },
            }}
          >
            <Box sx={{ fontSize: "1.2rem" }}>📱</Box>
            View All Google Reviews
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default Testimonials;
