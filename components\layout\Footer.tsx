"use client";
import React from "react";
import {
  Box,
  Container,
  Typography,
  Grid,
  IconButton,
  Divider,
  useTheme,
  useMediaQuery,
} from "@mui/material";
import {
  Instagram,
  Facebook,
  Phone,
  Email,
  LocationOn,
  AccessTime,
} from "@mui/icons-material";

const Footer: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  const socialLinks = [
    {
      name: "Instagram",
      icon: <Instagram />,
      url: "https://instagram.com/polo.restaurant.ch",
      color: "#E4405F",
    },
    {
      name: "Facebook",
      icon: <Facebook />,
      url: "https://facebook.com/polo.restaurant.ch",
      color: "#1877F2",
    },
  ];

  const quickLinks = [
    { name: "About Us", href: "#about" },
    { name: "Menu", href: "#menu" },
    { name: "Weekend Specials", href: "#specials" },
    { name: "Gallery", href: "#gallery" },
    { name: "Contact", href: "#contact" },
  ];

  const currentYear = new Date().getFullYear();

  return (
    <Box
      component="footer"
      sx={{
        background: `linear-gradient(135deg,
          ${theme.palette.text.primary} 0%,
          ${theme.palette.secondary.main} 100%)`,
        color: theme.palette.secondary.contrastText,
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Decorative Background Elements */}
      <Box
        sx={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: 0.05,
          backgroundImage: `
            radial-gradient(circle at 25% 25%, ${theme.palette.primary.main} 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, ${theme.palette.swissRed.main} 0%, transparent 50%)
          `,
          zIndex: 0,
        }}
      />

      {/* Traditional Pattern Overlay */}
      <Box
        sx={{
          position: "absolute",
          top: "20%",
          right: "10%",
          width: 120,
          height: 120,
          opacity: 0.03,
          background: `conic-gradient(from 0deg,
            ${theme.palette.afghanBlue.main},
            ${theme.palette.primary.main},
            ${theme.palette.swissRed.main},
            ${theme.palette.afghanBlue.main})`,
          borderRadius: "50%",
          filter: "blur(2px)",
          zIndex: 0,
        }}
      />

      <Container maxWidth="lg" sx={{ position: "relative", zIndex: 1 }}>
        {/* Main Footer Content */}
        <Box sx={{ py: 6 }}>
          <Grid container spacing={4}>
            {/* Restaurant Info */}
            <Grid item xs={12} md={4}>
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h4"
                  sx={{
                    fontFamily: "'Playfair Display', serif",
                    fontWeight: 700,
                    mb: 2,
                    background: `linear-gradient(135deg,
                      ${theme.palette.primary.light} 0%,
                      ${theme.palette.afghanBlue.light} 100%)`,
                    backgroundClip: "text",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                  }}
                >
                  Polo Restaurant
                </Typography>
                <Typography
                  variant="h6"
                  sx={{
                    fontFamily: "'Playfair Display', serif",
                    fontWeight: 400,
                    mb: 3,
                    color: theme.palette.afghanBlue.light,
                    fontSize: "1.2rem",
                    direction: "rtl",
                  }}
                >
                  رستوران پولو
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    color: "rgba(255, 255, 255, 0.8)",
                    lineHeight: 1.7,
                    mb: 3,
                  }}
                >
                  Authentic Afghan cuisine meets Swiss precision. Experience the
                  warmth of traditional hospitality in the heart of Switzerland.
                </Typography>

                {/* Social Media */}
                <Box sx={{ display: "flex", gap: 2 }}>
                  {socialLinks.map((social) => (
                    <IconButton
                      key={social.name}
                      component="a"
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      sx={{
                        width: 48,
                        height: 48,
                        background: `linear-gradient(135deg,
                          ${social.color} 0%,
                          ${social.color}CC 100%)`,
                        color: "white",
                        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                        "&:hover": {
                          transform: "translateY(-4px) scale(1.1)",
                          boxShadow: `0 8px 24px ${social.color}40`,
                        },
                      }}
                    >
                      {social.icon}
                    </IconButton>
                  ))}
                </Box>
              </Box>
            </Grid>

            {/* Quick Links */}
            <Grid item xs={12} md={2}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  mb: 3,
                  color: theme.palette.primary.light,
                }}
              >
                Quick Links
              </Typography>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 1.5 }}>
                {quickLinks.map((link) => (
                  <Box
                    key={link.name}
                    component="a"
                    href={link.href}
                    sx={{
                      color: "rgba(255, 255, 255, 0.8)",
                      textDecoration: "none",
                      fontSize: "0.9rem",
                      transition: "all 0.3s ease",
                      "&:hover": {
                        color: theme.palette.primary.light,
                        transform: "translateX(4px)",
                      },
                    }}
                  >
                    {link.name}
                  </Box>
                ))}
              </Box>
            </Grid>

            {/* Contact Info */}
            <Grid item xs={12} md={3}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  mb: 3,
                  color: theme.palette.primary.light,
                }}
              >
                Contact Info
              </Typography>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <LocationOn
                    sx={{
                      color: theme.palette.primary.light,
                      fontSize: "1.2rem",
                    }}
                  />
                  <Typography
                    variant="body2"
                    sx={{
                      color: "rgba(255, 255, 255, 0.8)",
                      lineHeight: 1.5,
                    }}
                  >
                    Bahnhofstrasse 123
                    <br />
                    8001 Zürich, Switzerland
                  </Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <Phone
                    sx={{
                      color: theme.palette.primary.light,
                      fontSize: "1.2rem",
                    }}
                  />
                  <Typography
                    variant="body2"
                    sx={{
                      color: "rgba(255, 255, 255, 0.8)",
                    }}
                  >
                    +41 44 123 4567
                  </Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <Email
                    sx={{
                      color: theme.palette.primary.light,
                      fontSize: "1.2rem",
                    }}
                  />
                  <Typography
                    variant="body2"
                    sx={{
                      color: "rgba(255, 255, 255, 0.8)",
                    }}
                  >
                    <EMAIL>
                  </Typography>
                </Box>
              </Box>
            </Grid>

            {/* Opening Hours */}
            <Grid item xs={12} md={3}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  mb: 3,
                  color: theme.palette.primary.light,
                }}
              >
                Opening Hours
              </Typography>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 1.5 }}>
                <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                  <Typography
                    variant="body2"
                    sx={{ color: "rgba(255, 255, 255, 0.8)" }}
                  >
                    Mon - Thu
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{ color: "white", fontWeight: 500 }}
                  >
                    11:00 - 22:00
                  </Typography>
                </Box>
                <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                  <Typography
                    variant="body2"
                    sx={{ color: "rgba(255, 255, 255, 0.8)" }}
                  >
                    Fri - Sun
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{ color: "white", fontWeight: 500 }}
                  >
                    11:00 - 23:00
                  </Typography>
                </Box>
                <Box
                  sx={{
                    mt: 1,
                    p: 2,
                    borderRadius: 2,
                    background: `linear-gradient(135deg,
                      ${theme.palette.swissRed.main}40 0%,
                      ${theme.palette.swissRed.main}20 100%)`,
                    border: `1px solid ${theme.palette.swissRed.main}60`,
                  }}
                >
                  <Typography
                    variant="caption"
                    sx={{
                      color: theme.palette.swissRed.light,
                      fontWeight: 600,
                      textTransform: "uppercase",
                      letterSpacing: "0.05em",
                    }}
                  >
                    Weekend Specials Available
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      color: "white",
                      fontWeight: 500,
                      mt: 0.5,
                    }}
                  >
                    Friday - Sunday
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Box>

        <Divider sx={{ borderColor: "rgba(255, 255, 255, 0.1)" }} />

        {/* Bottom Footer */}
        <Box
          sx={{
            py: 3,
            display: "flex",
            flexDirection: { xs: "column", md: "row" },
            justifyContent: "space-between",
            alignItems: "center",
            gap: 2,
          }}
        >
          <Typography
            variant="body2"
            sx={{
              color: "rgba(255, 255, 255, 0.6)",
              textAlign: { xs: "center", md: "left" },
            }}
          >
            © {currentYear} Polo Restaurant. All rights reserved.
          </Typography>

          <Box
            sx={{
              display: "flex",
              gap: 3,
              flexWrap: "wrap",
              justifyContent: { xs: "center", md: "flex-end" },
            }}
          >
            <Box
              component="a"
              href="#privacy"
              sx={{
                color: "rgba(255, 255, 255, 0.6)",
                textDecoration: "none",
                fontSize: "0.875rem",
                transition: "color 0.3s ease",
                "&:hover": {
                  color: theme.palette.primary.light,
                },
              }}
            >
              Privacy Policy
            </Box>
            <Box
              component="a"
              href="#terms"
              sx={{
                color: "rgba(255, 255, 255, 0.6)",
                textDecoration: "none",
                fontSize: "0.875rem",
                transition: "color 0.3s ease",
                "&:hover": {
                  color: theme.palette.primary.light,
                },
              }}
            >
              Terms of Service
            </Box>
            <Box
              component="a"
              href="#accessibility"
              sx={{
                color: "rgba(255, 255, 255, 0.6)",
                textDecoration: "none",
                fontSize: "0.875rem",
                transition: "color 0.3s ease",
                "&:hover": {
                  color: theme.palette.primary.light,
                },
              }}
            >
              Accessibility
            </Box>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
