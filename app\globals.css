:root {
  --background: transparent
  --foreground: #000000;

  /* Liquid Glass Effect Variables */
  --lg-bg-color: rgba(255, 255, 255, 0.25);
  --lg-highlight: rgba(255, 255, 255, 0.75);
  --lg-text: #000000;
  --lg-red: #fb4268;
  --lg-grey: #444739;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

/* Liquid Glass Effect Styles */
.glass-appbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: transparent;
  border-radius: 0 0 2rem 2rem;
  overflow: hidden;
  z-index: 1100;
  box-shadow: 0 6px 6px rgba(0, 0, 0, 0.2), 0 0 20px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 2.2);
}
.glass-container {
  position: relative;
  display: flex;
  align-items: center;
  background: transparent;
  border-radius: 2rem;
  overflow: hidden;
  flex: 1 1 auto;
  box-shadow: 0 6px 6px rgba(0, 0, 0, 0.2), 0 0 20px rgba(0, 0, 0, 0.1);
  color: var(--lg-text);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 2.2);
}

.glass-container--rounded {
  border-radius: 3rem;
}

.glass-container--large {
  flex: 1 1 auto;
}

.glass-container--medium {
  flex: 1 1 auto;
}

.glass-container--small {
  flex: 0 1 auto;
}

.glass-filter,
.glass-overlay,
.glass-specular {
  position: absolute;
  inset: 0;
  border-radius: inherit;
}

.glass-filter {
  z-index: 0;
  backdrop-filter: blur(4px);
  filter: saturate(120%) brightness(1.15);
}

.glass-overlay {
  z-index: 1;
  background: var(--lg-bg-color);
}

.glass-specular {
  z-index: 2;
  box-shadow: inset 1px 1px 0 var(--lg-highlight),
    inset 0 0 5px var(--lg-highlight);
}

.glass-content {
  position: relative;
  z-index: 3;
  display: flex;
  flex: 1 1 auto;
  align-items: center;
  justify-content: space-around;
  padding: 12px 28px;
  gap: 1rem;
  flex-wrap: wrap;
}

.glass-content__link {
  margin-bottom: -1px;
  margin-top: 6px;
  transition: transform 0.2s ease-out;
}

.glass-content__link img {
  width: 78px;
}

.glass-content__link:hover {
  transform: scale(1.1);
}

.glass-content__link:active {
  transform: scale(0.95);
}

.glass-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  color: var(--lg-grey);
  transition: color 0.3s ease;
  cursor: pointer;
}

.glass-item svg {
  fill: var(--lg-grey);
  height: 50px;
  margin-bottom: 0.25rem;
  filter: drop-shadow(0 0 3px rgba(255 255 255 / 0.25));
  transition: transform 0.25s ease-out;
}

.glass-item svg:hover {
  transform: scale(1.1);
}

.glass-item svg:active {
  transform: scale(0.95);
}

.glass-item--active {
  background: rgba(0, 0, 0, 0.25);
  color: var(--lg-red);
  margin: -8px -40px;
  padding: 0.5rem 2.5rem;
  border-radius: 3rem;
}

.glass-item--active svg {
  fill: var(--lg-red);
}
