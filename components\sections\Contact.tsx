"use client";
import React, { useState } from "react";
import {
  <PERSON>,
  Container,
  Typo<PERSON>,
  <PERSON>rid,
  Card,
  CardContent,
  TextField,
  Button,
  useTheme,
  useMediaQuery,
  IconButton,
  Divider,
} from "@mui/material";
import { Phone, Email, WhatsApp, LocationOn, AccessTime } from "@mui/icons-material";

const Contact: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    message: "",
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log("Form submitted:", formData);
  };

  const contactInfo = [
    {
      icon: <Phone />,
      title: "Phone",
      value: "+41 44 123 4567",
      action: "tel:+41441234567",
      color: theme.palette.primary.main,
    },
    {
      icon: <Email />,
      title: "Email",
      value: "<EMAIL>",
      action: "mailto:<EMAIL>",
      color: theme.palette.afghanBlue.main,
    },
    {
      icon: <WhatsApp />,
      title: "WhatsApp",
      value: "+41 79 123 4567",
      action: "https://wa.me/41791234567",
      color: "#25D366",
    },
  ];

  return (
    <Box
      component="section"
      sx={{
        py: { xs: 8, md: 12 },
        position: "relative",
        background: `linear-gradient(180deg, 
          ${theme.palette.background.paper} 0%, 
          ${theme.palette.primary[50]} 100%)`,
      }}
    >
      {/* Background Elements */}
      <Box
        sx={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: 0.02,
          backgroundImage: `
            radial-gradient(circle at 30% 30%, ${theme.palette.primary.main} 0%, transparent 50%),
            radial-gradient(circle at 70% 70%, ${theme.palette.swissRed.main} 0%, transparent 50%)
          `,
          zIndex: 0,
        }}
      />

      <Container maxWidth="lg" sx={{ position: "relative", zIndex: 1 }}>
        {/* Section Header */}
        <Box sx={{ textAlign: "center", mb: 8 }}>
          <Box
            sx={{
              display: "inline-flex",
              alignItems: "center",
              px: 3,
              py: 1,
              mb: 3,
              borderRadius: "50px",
              background: `linear-gradient(135deg, 
                ${theme.palette.swissRed[100]} 0%, 
                ${theme.palette.swissRed[50]} 100%)`,
              border: `1px solid ${theme.palette.swissRed[200]}`,
              boxShadow: theme.customShadows.warm,
            }}
          >
            <Typography
              variant="caption"
              sx={{
                fontWeight: 600,
                color: theme.palette.swissRed.dark,
                textTransform: "uppercase",
                letterSpacing: "0.1em",
              }}
            >
              📍 Contact Us
            </Typography>
          </Box>

          <Typography
            variant="h2"
            sx={{
              mb: 3,
              fontFamily: "'Playfair Display', serif",
              fontWeight: 700,
              color: theme.palette.text.primary,
              lineHeight: 1.2,
            }}
          >
            Visit Us Today
          </Typography>

          <Typography
            variant="body1"
            sx={{
              color: theme.palette.text.secondary,
              maxWidth: 600,
              mx: "auto",
              fontSize: "1.1rem",
              lineHeight: 1.7,
            }}
          >
            Experience authentic Afghan cuisine in the heart of Switzerland. 
            We're here to serve you with warmth and hospitality.
          </Typography>
        </Box>

        <Grid container spacing={6}>
          {/* Contact Information & Map */}
          <Grid item xs={12} lg={6}>
            {/* Contact Cards */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
              {contactInfo.map((contact, index) => (
                <Grid item xs={12} sm={4} lg={12} key={index}>
                  <Card
                    component="a"
                    href={contact.action}
                    target={contact.action.startsWith("http") ? "_blank" : undefined}
                    rel={contact.action.startsWith("http") ? "noopener noreferrer" : undefined}
                    sx={{
                      textDecoration: "none",
                      borderRadius: 3,
                      overflow: "hidden",
                      background: `linear-gradient(135deg, 
                        rgba(255, 255, 255, 0.9) 0%, 
                        rgba(255, 255, 255, 0.7) 100%)`,
                      backdropFilter: "blur(10px)",
                      border: `1px solid rgba(255, 255, 255, 0.3)`,
                      boxShadow: theme.customShadows.glass,
                      transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                      "&:hover": {
                        transform: "translateY(-4px)",
                        boxShadow: theme.customShadows.glassHover,
                      },
                    }}
                  >
                    <CardContent
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: 3,
                        p: 3,
                      }}
                    >
                      <Box
                        sx={{
                          width: 48,
                          height: 48,
                          borderRadius: "50%",
                          background: `linear-gradient(135deg, 
                            ${contact.color} 0%, 
                            ${contact.color}CC 100%)`,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          color: "white",
                          flexShrink: 0,
                        }}
                      >
                        {contact.icon}
                      </Box>
                      <Box>
                        <Typography
                          variant="h6"
                          sx={{
                            fontWeight: 600,
                            color: theme.palette.text.primary,
                            mb: 0.5,
                          }}
                        >
                          {contact.title}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{
                            color: theme.palette.text.secondary,
                            fontWeight: 500,
                          }}
                        >
                          {contact.value}
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>

            {/* Address & Hours */}
            <Card
              sx={{
                borderRadius: 4,
                overflow: "hidden",
                background: `linear-gradient(135deg, 
                  ${theme.palette.primary[100]} 0%, 
                  ${theme.palette.primary[50]} 100%)`,
                border: `1px solid ${theme.palette.primary[200]}`,
                boxShadow: theme.customShadows.alpine,
              }}
            >
              <CardContent sx={{ p: 4 }}>
                {/* Address */}
                <Box sx={{ display: "flex", alignItems: "flex-start", gap: 2, mb: 4 }}>
                  <LocationOn sx={{ color: theme.palette.primary.main, mt: 0.5 }} />
                  <Box>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        color: theme.palette.text.primary,
                        mb: 1,
                      }}
                    >
                      Address
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: theme.palette.text.secondary,
                        lineHeight: 1.6,
                      }}
                    >
                      Bahnhofstrasse 123<br />
                      8001 Zürich, Switzerland
                    </Typography>
                  </Box>
                </Box>

                <Divider sx={{ my: 3, opacity: 0.3 }} />

                {/* Opening Hours */}
                <Box sx={{ display: "flex", alignItems: "flex-start", gap: 2 }}>
                  <AccessTime sx={{ color: theme.palette.primary.main, mt: 0.5 }} />
                  <Box>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        color: theme.palette.text.primary,
                        mb: 2,
                      }}
                    >
                      Opening Hours
                    </Typography>
                    <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
                      <Box sx={{ display: "flex", justifyContent: "space-between", minWidth: 200 }}>
                        <Typography variant="body2" sx={{ color: theme.palette.text.secondary }}>
                          Monday - Thursday
                        </Typography>
                        <Typography variant="body2" sx={{ color: theme.palette.text.primary, fontWeight: 500 }}>
                          11:00 - 22:00
                        </Typography>
                      </Box>
                      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                        <Typography variant="body2" sx={{ color: theme.palette.text.secondary }}>
                          Friday - Sunday
                        </Typography>
                        <Typography variant="body2" sx={{ color: theme.palette.text.primary, fontWeight: 500 }}>
                          11:00 - 23:00
                        </Typography>
                      </Box>
                      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                        <Typography variant="body2" sx={{ color: theme.palette.swissRed.main, fontWeight: 600 }}>
                          Weekend Specials
                        </Typography>
                        <Typography variant="body2" sx={{ color: theme.palette.swissRed.main, fontWeight: 600 }}>
                          Fri - Sun
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </CardContent>
            </Card>

            {/* Google Maps Placeholder */}
            <Card
              sx={{
                mt: 4,
                borderRadius: 4,
                overflow: "hidden",
                boxShadow: theme.customShadows.ornate,
              }}
            >
              <Box
                sx={{
                  height: 300,
                  background: `linear-gradient(135deg, 
                    ${theme.palette.primary[200]} 0%, 
                    ${theme.palette.primary[100]} 100%)`,
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  textAlign: "center",
                  p: 4,
                  position: "relative",
                  cursor: "pointer",
                  transition: "all 0.3s ease",
                  "&:hover": {
                    transform: "scale(1.02)",
                  },
                }}
                onClick={() => window.open("https://maps.google.com/?q=Polo+Restaurant+Zurich", "_blank")}
              >
                <Box sx={{ fontSize: "3rem", mb: 2 }}>🗺️</Box>
                <Typography
                  variant="h6"
                  sx={{
                    fontFamily: "'Playfair Display', serif",
                    fontWeight: 600,
                    color: theme.palette.text.primary,
                    mb: 1,
                  }}
                >
                  View on Google Maps
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    color: theme.palette.text.secondary,
                  }}
                >
                  Click to open in Google Maps
                </Typography>
              </Box>
            </Card>
          </Grid>

          {/* Contact Form */}
          <Grid item xs={12} lg={6}>
            <Card
              sx={{
                borderRadius: 4,
                overflow: "hidden",
                background: `linear-gradient(135deg, 
                  rgba(255, 255, 255, 0.95) 0%, 
                  rgba(255, 255, 255, 0.85) 100%)`,
                backdropFilter: "blur(20px)",
                border: `1px solid rgba(255, 255, 255, 0.3)`,
                boxShadow: theme.customShadows.glass,
              }}
            >
              <CardContent sx={{ p: 4 }}>
                <Typography
                  variant="h5"
                  sx={{
                    fontFamily: "'Playfair Display', serif",
                    fontWeight: 600,
                    color: theme.palette.text.primary,
                    mb: 3,
                    textAlign: "center",
                  }}
                >
                  Send Us a Message
                </Typography>

                <Box component="form" onSubmit={handleSubmit} sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
                  <TextField
                    fullWidth
                    label="Full Name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        borderRadius: 2,
                        background: "rgba(255, 255, 255, 0.8)",
                      },
                    }}
                  />

                  <TextField
                    fullWidth
                    label="Email Address"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        borderRadius: 2,
                        background: "rgba(255, 255, 255, 0.8)",
                      },
                    }}
                  />

                  <TextField
                    fullWidth
                    label="Phone Number"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        borderRadius: 2,
                        background: "rgba(255, 255, 255, 0.8)",
                      },
                    }}
                  />

                  <TextField
                    fullWidth
                    label="Message"
                    name="message"
                    multiline
                    rows={4}
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        borderRadius: 2,
                        background: "rgba(255, 255, 255, 0.8)",
                      },
                    }}
                  />

                  <Button
                    type="submit"
                    variant="contained"
                    size="large"
                    sx={{
                      py: 2,
                      borderRadius: "50px",
                      fontSize: "1.1rem",
                      fontWeight: 600,
                      textTransform: "none",
                      background: `linear-gradient(135deg, 
                        ${theme.palette.afghanBlue.main} 0%, 
                        ${theme.palette.afghanBlue.dark} 100%)`,
                      color: theme.palette.afghanBlue.contrastText,
                      boxShadow: theme.customShadows.ornate,
                      transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                      "&:hover": {
                        transform: "translateY(-2px)",
                        boxShadow: theme.customShadows.ornateHover,
                      },
                    }}
                  >
                    📧 Send Message
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default Contact;
