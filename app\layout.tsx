import type { Metadata } from "next";
import "./globals.css";
import "../styles/animations.css";
import { AppRouterCacheProvider } from "@mui/material-nextjs/v15-appRouter";
import ThemeCustomization from "@/themes";
import { StyledEngineProvider } from "@mui/material";
import Header from "@/components/layout/Header";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body>
        <StyledEngineProvider injectFirst>
          <AppRouterCacheProvider options={{ key: "css" }}>
            <ThemeCustomization>
              <Header />
              {children}
            </ThemeCustomization>
          </AppRouterCacheProvider>
        </StyledEngineProvider>
      </body>
    </html>
  );
}
